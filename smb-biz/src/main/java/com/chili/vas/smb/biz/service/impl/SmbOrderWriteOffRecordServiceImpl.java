package com.chili.vas.smb.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import com.chili.vas.smb.biz.mapper.SmbOrderWriteOffRecordMapper;
import com.chili.vas.smb.biz.service.SmbOrderWriteOffRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WriteOffRecordServiceImpl 类
 *
 * <AUTHOR>
 * @since 2025/6/23 16:11
 */
@Service
public class SmbOrderWriteOffRecordServiceImpl extends ServiceImpl<SmbOrderWriteOffRecordMapper, SmbOrderWriteOffRecord> implements SmbOrderWriteOffRecordService {

    @Autowired
    SmbOrderWriteOffRecordMapper smbOrderWriteOffRecordMapper;

    @Override
    public SmbOrderWriteOffRecord findOneBySmbOrderNo(String smbOrderNo) {
        return smbOrderWriteOffRecordMapper.findOneBySmbOrderNo(smbOrderNo);
    }

    @Override
    public Integer selectCountBySmbOrderNoAndStatus(String smbOrderNo, Integer status) {
        return smbOrderWriteOffRecordMapper.selectCountBySmbOrderNoAndStatus(smbOrderNo, status);
    }

    @Override
    public Integer updateWriteOffInfoBySmbOrderNo(SmbOrderWriteOffRecord smbOrderWriteOffRecord, Integer oldStatus) {
        return smbOrderWriteOffRecordMapper.updateWriteOffInfoBySmbOrderNo(smbOrderWriteOffRecord,oldStatus);
    }

    @Override
    public SmbOrderWriteOffRecord queryLastWriteOffRecordBySmbOrderNo(String smbOrderNo) {
        return baseMapper.queryLastWriteOffRecordBySmbOrderNo(smbOrderNo);
    }

}
