package com.chili.vas.smb.biz.ext.smb.manager;

import com.chili.vas.smb.biz.ext.smb.constant.SmbConstant;
import com.snb.fsos.sdk.SnbSdk;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @createDate 2025/5/23 14:28
 */
@Slf4j
public class DefaultSmbManager implements SmbManager {

    private final SnbSdk chiliSnbSdk;

    private final SnbSdk poyanSnbSdk;

    public DefaultSmbManager(SnbSdk chiliSnbSdk,SnbSdk poyanSnbSdk) {
        this.chiliSnbSdk = chiliSnbSdk;
        this.poyanSnbSdk = poyanSnbSdk;
    }

    @Override
    public InputStream downloadStream(String serialNo, String sceneCode, String fileType, String remoteFilePath) throws Exception {
        Map response = chiliSnbSdk.downloadFile(serialNo, SmbConstant.TransCode.SINGLE_FILE_DOWNLOAD, SmbConstant.VERSION, sceneCode, fileType, remoteFilePath);
        log.info("smb download response: {}",response);
        boolean result = (Boolean) response.get("downloadResult");
        if (result) {
            return (InputStream) response.get("inputStream");
        }
        return null;
    }


    @Override
    public Map uploadStream(String serialNo, String sceneCode, String fileName, InputStream inputStream, String fileType) throws Exception {
        List<Map<String,Object>> filesList = new ArrayList<>();
        Map<String,Object> fileMap = new HashMap<>();
        fileMap.put("inputStream", inputStream);
        fileMap.put("fileName", fileName);
        filesList.add(fileMap);

        //这个是file_digestIndex_type
        Map<String, String> fileDigestMap = new HashMap<>();
        fileDigestMap.put("file_0_type", fileType);

        Map<String, Object> payload = new HashMap<>();
        payload.put("fileDigestMap", fileDigestMap);

        //这个是场景码
        payload.put("sceneCode", sceneCode);

        Map respMap = chiliSnbSdk.uploadFileStream(serialNo, SmbConstant.TransCode.FILE_STREAM_UPLOAD, SmbConstant.VERSION, payload, filesList);
        log.info("smb upload response: {}",respMap);
        return respMap;
    }


    @Override
    public Map fundReceivedCallback(String serialNo, Map<String, Object> playload) throws Exception {
        Map respMap = poyanSnbSdk.sendRequest(serialNo, SmbConstant.TransCode.NOTIFICATION_OF_FUND_ARRIVAL_CALLBACK, SmbConstant.VERSION, null, null, null, null, null, null, null, playload);
        log.info("smb fund.received.callback response: {}",respMap);
        return respMap;
    }

    @Override
    public Map resultFileCallback(String serialNo, Map<String, Object> playload) throws Exception {
        Map respMap = chiliSnbSdk.sendRequest(serialNo, SmbConstant.TransCode.RESULT_FILE_NOTIFICATION_CALLBACK, SmbConstant.VERSION, null, null, null, null, null, null, null, playload);
        log.info("smb resulting.file.callback response: {}",respMap);
        return respMap;
    }

    @Override
    public String getChiliChannelId() {
        return chiliSnbSdk.getChannelId();
    }

    @Override
    public String getChiliAppCode() {
        return chiliSnbSdk.getAppCode();
    }

    @Override
    public String getChiliMerchantId() {
        return chiliSnbSdk.getMerchantId();
    }

    @Override
    public String getPoyanChannelId() {
        return poyanSnbSdk.getChannelId();
    }

    @Override
    public String getPoyanAppCode() {
        return poyanSnbSdk.getAppCode();
    }

    @Override
    public String getPoyanMerchantId() {
        return poyanSnbSdk.getMerchantId();
    }


}
