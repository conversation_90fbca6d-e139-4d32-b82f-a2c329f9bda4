package com.chili.vas.smb.biz.job.context;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.chili.vas.smb.biz.entity.Job;

import lombok.Data;

@Data
public class DefaultSystemJobContext implements SystemJobContext {

    private final Job job;

    private final Map<String, Object> paramMap;
    
    private final Map<String, Object> resultMap;
    
    private final Map<String, Exception> exceptionMap;
    
    public DefaultSystemJobContext(Job job) {
        this.job = job;
        this.paramMap = new ConcurrentHashMap<>();
        this.resultMap = new ConcurrentHashMap<>();
        this.exceptionMap = new ConcurrentHashMap<>();
    }

    @Override
    public Job getJob() {
        return job;
    }

    @Override
    public Map<String, Object> getParamMap() {
        return paramMap;
    }

    @Override
    public Map<String, Object> getResultMap() {
        return resultMap;
    }

    @Override
    public Map<String, ? extends Exception> getExceptionMap() {
        return exceptionMap;
    }

    @Override
    public SystemJobContext addParam(String key, Object value) {
        paramMap.put(key, value);
        return this;
    }

    @Override
    public SystemJobContext addResult(String key, Object value) {
        resultMap.put(key, value);
        return this;
    }

    @Override
    public SystemJobContext addException(String key, Exception exception) {
        exceptionMap.put(key, exception);
        return this;
    }

}
