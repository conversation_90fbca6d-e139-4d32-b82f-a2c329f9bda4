package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/5/28 15:47
 */
@Getter
public enum SmbOrderWriteOffStatusEnum {

    // 核销状态(0.待核销 1.已核销)
    WRIETE_OFF_NO(0, "待核销"),
    WRITE_OFF_YES(1, "已核销"),

    WRITE_OFF_WAIT(2, "核销中"),
    ;


    private Integer value;
    private String desc;

    SmbOrderWriteOffStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public static SmbOrderWriteOffStatusEnum getSmbOrderWriteOffStatusEnumByValue(Integer value) {
        for (SmbOrderWriteOffStatusEnum smbOrderWriteOffStatusEnum : SmbOrderWriteOffStatusEnum.values()) {
            if (smbOrderWriteOffStatusEnum.getValue().equals(value)) {
                return smbOrderWriteOffStatusEnum;
            }
        }
        return null;
    }

}
