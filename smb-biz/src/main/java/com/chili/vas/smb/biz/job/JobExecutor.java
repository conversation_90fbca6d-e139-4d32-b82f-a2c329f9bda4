package com.chili.vas.smb.biz.job;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;

/**
 * 任务执行器
 */
public interface JobExecutor {

    /**
     * 执行任务
     * @param job 任务
     */
    void execute(Job job);

    /**
     * 执行任务
     * @param bizId 业务ID
     * @param sceneCode 业务场景编码
     */
    void execute(@Nullable Long bizId, @NonNull String sceneCode);

    /**
     * 执行任务
     * @param sceneCode 业务场景编码
     */
    void execute(@Nullable String sceneCode, @NonNull JobStatusEnum status);
}
