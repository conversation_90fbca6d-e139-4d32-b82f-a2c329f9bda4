package com.chili.vas.smb.biz.ext.qunqiu.exec;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

public class HttpServiceExceptionTranslate extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        HttpServiceException publishServiceException = (HttpServiceException) throwable;
        return ResolvedResultBuilder.build(publishServiceException.getCode(), publishServiceException.getMessage(), HttpStatusCode.INTERNAL_SERVER_ERROR);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof HttpServiceException;
    }
}
