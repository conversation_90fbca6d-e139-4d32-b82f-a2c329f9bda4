package com.chili.vas.smb.biz.util;

import java.security.SecureRandom;

public class RandomUtil {

    /**
     * 卡密可用的字符集：数字（0-9）和 大写字母（A-Z），移除了 'I' 和 'O'。
     * 总共 10 + 26 - 2 = 34个字符。
     */
    private static final String CHARACTERS = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";

    /**
     * 使用加密强度的随机数生成器。
     * 推荐在类级别只创建一个实例并复用它，以避免性能开销和种子问题。
     */
    private static final SecureRandom random = new SecureRandom();


    public static String randomStr(int length) {
        if (length < 1) {
            throw new IllegalArgumentException("random length must be greater than 0");
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 从字符集中随机选择一个字符
            int randomIndex = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }
        return sb.toString();
    }
}
