package com.chili.vas.smb.biz.service.impl;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;
import com.chili.vas.smb.biz.entity.JobSetting;
import com.chili.vas.smb.biz.mapper.JobMapper;
import com.chili.vas.smb.biz.service.JobRecordService;
import com.chili.vas.smb.biz.service.JobService;
import com.chili.vas.smb.biz.service.JobSettingService;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
@Slf4j
@Service
public class JobServiceImpl extends ServiceImpl<JobMapper, Job> implements JobService {

    @Autowired
    private JobSettingService jobSettingService;

    @Autowired
    private JobRecordService jobRecordService;

    @Autowired
    private IdGenerator idGenerator;

    @Override
    public void initJob(String sceneCode, Long bizCode) {
        List<JobSetting> jobSettings = jobSettingService.listBySceneCode(sceneCode);
        if (CollUtil.isEmpty(jobSettings)) {
            throw new RuntimeException("job haven't been configured");
        }
        List<Job> jobs = jobSettings.stream().map(it -> {
                    Job job = new Job();
                    job.setId(idGenerator.nextId());
                    job.setBizId(bizCode);
                    job.setSceneCode(sceneCode);
                    job.setJobCode(it.getCode());
                    job.setJobName(it.getName());
                    job.setStatus(JobStatusEnum.PENDING.getValue());
                    job.setSort(it.getSort());
                    job.setDeleted(0);
                    return job;
                }).collect(Collectors.toList());
        saveByBatch(jobs);
    }

    @Override
    public void saveByBatch(Collection<Job> jobs) {
        baseMapper.saveBatch(jobs);
    }

    @Transactional
    @Override
    public void setStart(Job job) {
        job.setStatus(JobStatusEnum.PROCESSING.getValue());
        job.setStartTime(LocalDateTime.now());
        updateById(job);
        jobRecordService.createByJob(job);
    }

    @Transactional
    @Override
    public void setEnd(Job job, JobStatusEnum status, Exception e) {
        job.setStatus(status.getValue());
        job.setEndTime(LocalDateTime.now());
        updateById(job);
        jobRecordService.updateStatus(job.getId(), status, e);
    }

    @Override
    public List<Job> listByBizIdAndSceneCode(@NonNull Long bizId, @NonNull String sceneCode,
            @Nullable JobStatusEnum status) {
        return baseMapper.selectListByBizIdAndSceneCode(bizId, sceneCode, status == null ? null : status.getValue());
    }

    @Override
    public List<Job> listBySceneCodeAndStatus(@Nullable String sceneCode, @Nullable JobStatusEnum status) {
        return baseMapper.selectListByBizIdAndSceneCode(null, sceneCode, status == null ? null : status.getValue());
    }

}
