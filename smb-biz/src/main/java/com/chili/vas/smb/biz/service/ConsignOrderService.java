package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import jakarta.servlet.http.HttpServletResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 寄售订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
public interface ConsignOrderService extends IService<ConsignOrder> {

    Integer updateStatusAndConsignInfo(String orderNo,
                                       Integer oldStatus,
                                       Integer newStatus,
                                       String extTradeNo,
                                       String payChannel,
                                       String retCode,
                                       LocalDateTime transactionTime,
                                       LocalDateTime transactionFinishTime,
                                       String remark);


    ConsignOrder getByOrderNo(String orderNo);

    Integer updateManualRetryByOrderNo(String orderNo, boolean manualRetry);

    List<ConsignOrder> selectWaitHalfHourList();


    /**
     * 更新状态
     * @param consignOrderNo
     * @param oldStatus
     * @param newStatus
     * @return
     */
    Integer updateStatus(String consignOrderNo, Integer oldStatus, Integer newStatus);


    Page<ConsignOrder> getCosignOrderPage(ConsignOrder consignOrder, Integer pageNum, Integer pageSize, LocalDateTime startTime, LocalDateTime endTime);

    Page<ConsignOrder> paymentRecord(String smbOrderNo, int pageNum, int pageSize);


    void  export( ConsignOrder consignOrder, LocalDateTime startTime, LocalDateTime endTime, HttpServletResponse httpServletResponse);


    ConsignOrder getLastOrderBySmbOrderNo(String smbOrderNo);
}
