package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.entity.ConsignOrderChannelErrorMsg;
import com.chili.vas.smb.biz.mapper.ConsignOrderChannelErrorMsgMapper;
import com.chili.vas.smb.biz.service.ConsignOrderChannelErrorMsgService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 寄售代付渠道错误信息维护表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
@Service
public class ConsignOrderChannelErrorMsgServiceImpl extends ServiceImpl<ConsignOrderChannelErrorMsgMapper, ConsignOrderChannelErrorMsg> implements ConsignOrderChannelErrorMsgService {

}
