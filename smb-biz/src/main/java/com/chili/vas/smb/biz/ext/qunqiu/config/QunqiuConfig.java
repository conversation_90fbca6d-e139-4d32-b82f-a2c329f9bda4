package com.chili.vas.smb.biz.ext.qunqiu.config;

import com.chili.vas.smb.biz.ext.qunqiu.client.DefaultQunqiuClient;
import com.chili.vas.smb.biz.ext.qunqiu.client.QunqiuClient;
import com.chili.vas.smb.biz.ext.qunqiu.properties.QunqiuProperties;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(QunqiuProperties.class)
public class QunqiuConfig {

    @Bean
    public QunqiuClient jishouClient(HttpRequestExecutor httpRequestExecutor, QunqiuProperties qunqiuProperties) {
        return new DefaultQunqiuClient(httpRequestExecutor, qunqiuProperties);
    }

}
