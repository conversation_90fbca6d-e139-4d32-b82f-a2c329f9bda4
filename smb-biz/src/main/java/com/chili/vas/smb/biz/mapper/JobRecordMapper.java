package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.JobRecord;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 任务执行记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
public interface JobRecordMapper extends BaseMapper<JobRecord> {

    /**
     * 根据任务ID查询最新一条记录
     * @param jobId
     * @return
     */
    JobRecord findLastOneByJobId(@Param("jobId") Long jobId);

}
