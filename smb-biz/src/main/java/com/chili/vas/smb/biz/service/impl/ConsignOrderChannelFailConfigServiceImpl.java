package com.chili.vas.smb.biz.service.impl;

import cn.hutool.core.util.StrUtil;

import com.chili.vas.smb.biz.common.enums.ConsignOrderManualEnum;
import com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig;
import com.chili.vas.smb.biz.mapper.ConsignOrderChannelFailConfigMapper;
import com.chili.vas.smb.biz.service.ConsignOrderChannelFailConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 寄售订单错误码操作表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 18:23:07
 */
@Service
public class ConsignOrderChannelFailConfigServiceImpl extends ServiceImpl<ConsignOrderChannelFailConfigMapper, ConsignOrderChannelFailConfig> implements ConsignOrderChannelFailConfigService {

    @Autowired
    ConsignOrderChannelFailConfigMapper consignOrderChannelFailConfigMapper;

    @Override
    public ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorCode(String payChannel,String errorCode) {
        return consignOrderChannelFailConfigMapper.getConsignOrderChannelFailConfigByErrorCode(payChannel,errorCode);
    }

    @Override
    public ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorMsg(String payChannel,String errorMsg) {
        return consignOrderChannelFailConfigMapper.getConsignOrderChannelFailConfigByErrorMsg(payChannel,errorMsg);
    }

    @Override
    public ConsignOrderChannelFailConfig getFailConfigByErrorCodeMsg(String payChannel,String errorCode, String errorMsg) {
        if (StrUtil.isNotBlank(errorCode)) {
            return getConsignOrderChannelFailConfigByErrorCode(payChannel,errorCode);
        }
        if (StrUtil.isNotBlank(errorCode)) {
            return getConsignOrderChannelFailConfigByErrorMsg(payChannel,errorMsg);
        }
        return null;
    }

    @Override
    public boolean getManual(String payChannel, String errorCode, String errorMsg) {
        ConsignOrderChannelFailConfig failConfig = getFailConfigByErrorCodeMsg(payChannel, errorCode, errorMsg);
        if (failConfig != null) {
            return ConsignOrderManualEnum.YES.getValue().equals(failConfig.getManual());
        }
        return false;
    }
}
