package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * WriteOffRecord 类
 *
 * <AUTHOR>
 * @since 2025/6/23 16:05
 */
@Data
public class SmbOrderWriteOffRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 苏商订单号
     */
    private String smbOrderNo;
    /**
     * 流水号
     */
    private String depositSerno;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 响应结果
     */
    private String responseResult;

    /**+
     * 结果码
     */
    private String resultCode;

    /**
     * 状态(0.待核销 1.核销中  2.核销成功  9.核销失败)
     */
    private Integer status;

    /**
     * 手动备注
     */
    private String manualRemark;

    /**
     * 手动核销通知结果
     */
    private Integer manualStatus;

    /**
     * 苏商通知状态
     */
    private String notifyStatus;

    /**
     * 是否是手动核销（0.不是 1.是）
     */
    private Boolean manualFlag;

}
