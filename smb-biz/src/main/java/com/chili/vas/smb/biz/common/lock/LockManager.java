package com.chili.vas.smb.biz.common.lock;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;

/**
 * <AUTHOR>
 * @create 2024/3/20 17:29
 */
public interface LockManager {


    /**
     * 获取锁
     * @param key
     * @return
     */
    DLock getLock(String key);

    /**
     * 获取领取订单锁
     * @param orderTradeNo
     * @return
     */
    default DLock getReceiveOrderLock(String orderTradeNo) {
        return getLock("LOCK:RECEIVE:SMB:ORDER:" + orderTradeNo);
    }

    /**
     * 定时补偿查询群秋订单锁
     * @return
     */
    default DLock getScheduleQueryQunqiuOrderLock() {
        return getLock("LOCK:SCHEDULE:SMD:QUNQIU:ORDER");
    }

    /**
     * 待执行任务锁
     * @return
     */
    default DLock getScheduleSmbPendingJobExecutionLock() {
        return getLock("LOCK:SCHEDULE:SMB:PENDING:JOB:EXECUTION");
    }

    default DLock getManualWriteOffLock(String orderNo){
        return getLock("LOCK:Manual:Write:Off:"+orderNo );
    }


    default DLock getOrderRetryLock(String orderNo){
        return getLock("LOCK:Order:Retry:"+orderNo );
    }

}
