package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 寄售订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
@Data
@TableName("consign_order")
public class ConsignOrder implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 1:删除；0:未删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 0：待处理；1：处理中; 2：成功；11：失败
     */
    private Integer status;

    /**
     * 代付人姓名
     */
    private String name;

    /**
     * 代付人银行卡号
     */
    private String bankAccountNo;

    /**
     * 寄售金额（分）寄售平台的为元
     */
    private Integer amount;

    /**
     * 苏商订单号
     */
    private String smbOrderNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 代付渠道编号。民生、连连、拉卡拉
     */
    private String payChannel;

    /**
     * 代付渠道的返回结果码，不同代付渠道，结果码不一样
     */
    private String retCode;

    /**
     * 最新发起寄售时间
     */
    private LocalDateTime transactionTime;

    /**
     * 寄售完成时间
     */
    private LocalDateTime transactionFinishTime;

    /**
     * 是否可以手动重试（1：可以；0不可以）
     */
    private Integer manualRetry;

    /**
     * 外部交易号
     */
    private String extTradeNo;

    /**
     * 开户行名
     */
    private String bankAccountName;

    /**
     * 开户行号
     */
    private String bankAccountNum;

    /**
     * 1 - 对公。0 - 对私。
     */
    private String flagCard;

    /**
     * 城市编码
     */
    private String cityCode;


    @Override
    public String toString() {
        return "ConsignOrder{" +
                "id = " + id +
                ", createTime = " + createTime +
                ", updateTime = " + updateTime +
                ", deleted = " + deleted +
                ", orderNo = " + orderNo +
                ", status = " + status +
                ", name = " + name +
                ", bankAccountNo = " + bankAccountNo +
                ", amount = " + amount +
                ", smbOrderNo = " + smbOrderNo +
                ", remark = " + remark +
                ", payChannel = " + payChannel +
                ", retCode = " + retCode +
                ", transactionTime = " + transactionTime +
                ", transactionFinishTime = " + transactionFinishTime +
                ", manualRetry = " + manualRetry +
                ", extTradeNo = " + extTradeNo +
                ", bankAccountName = " + bankAccountName +
                ", bankAccountNum = " + bankAccountNum +
                ", flagCard = " + flagCard +
                ", cityCode = " + cityCode +
                "}";
    }
}
