package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 寄售订单错误码操作表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 18:23:07
 */
@TableName("consign_order_channel_fail_config")
public class ConsignOrderChannelFailConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识;删除逻辑标识（1：删除   0：不删除）
     */
    @TableLogic
    private Integer deleted;

    /**
     * 1：手动操作；0：不能手动操作
     */
    private Integer manual;

    /**
     * 1：通知亿联；0：不通知亿联
     */
    private Integer notify;

    /**
     * 错误码ID
     */
    private Long consignOrderChannelErrorMsgId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getManual() {
        return manual;
    }

    public void setManual(Integer manual) {
        this.manual = manual;
    }

    public Integer getNotify() {
        return notify;
    }

    public void setNotify(Integer notify) {
        this.notify = notify;
    }

    public Long getConsignOrderChannelErrorMsgId() {
        return consignOrderChannelErrorMsgId;
    }

    public void setConsignOrderChannelErrorMsgId(Long consignOrderChannelErrorMsgId) {
        this.consignOrderChannelErrorMsgId = consignOrderChannelErrorMsgId;
    }

    @Override
    public String toString() {
        return "ConsignOrderChannelFailConfig{" +
            "id = " + id +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
            ", deleted = " + deleted +
            ", manual = " + manual +
            ", notify = " + notify +
            ", consignOrderChannelErrorMsgId = " + consignOrderChannelErrorMsgId +
        "}";
    }
}
