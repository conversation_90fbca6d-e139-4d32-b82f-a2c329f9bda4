package com.chili.vas.smb.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chili.vas.smb.biz.entity.TenantClientInfo;
import org.apache.ibatis.annotations.Param;

/**
 * TenantClientInfoMapper 接口
 *
 * <AUTHOR>
 * @since 2025/6/19 15:57
 */
public interface TenantClientInfoMapper extends BaseMapper<TenantClientInfo> {


    /**
     * 根据clientId查询client信息
     * @param clientId
     * @param enable
     * @return
     */
    TenantClientInfo selectByClintId(@Param("clientId") String clientId, @Param("enable") Boolean enable);

}
