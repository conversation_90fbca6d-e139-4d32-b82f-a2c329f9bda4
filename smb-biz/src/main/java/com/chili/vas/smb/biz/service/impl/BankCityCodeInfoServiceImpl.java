package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.entity.BankCityCodeInfo;
import com.chili.vas.smb.biz.mapper.BankCityCodeInfoMapper;
import com.chili.vas.smb.biz.service.BankCityCodeInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26 16:35:24
 */
@Service
public class BankCityCodeInfoServiceImpl extends ServiceImpl<BankCityCodeInfoMapper, BankCityCodeInfo> implements BankCityCodeInfoService {

    @Resource
    BankCityCodeInfoMapper bankCityCodeInfoMapper;

    @Override
    public BankCityCodeInfo findOneByBraBankName(String braBankName) {
        return bankCityCodeInfoMapper.findOneByBraBankName(braBankName);
    }

    @Override
    public String findCityCodeByBraBankName(String braBankName) {
        return bankCityCodeInfoMapper.findCityCodeByBraBankName(braBankName);
    }


}
