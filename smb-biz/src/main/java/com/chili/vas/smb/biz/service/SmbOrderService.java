package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.entity.SmbOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * <p>
 * 苏商订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
public interface SmbOrderService extends IService<SmbOrder> {

    List<String> queryRepetitionSmbOrderSerno(List<String> depositSernos);

    Integer insertBatch( List<SmbOrder> smbOrders);

    Integer updateDeletedByBatchNo( String batchNo, Integer deleted);

    Integer updateStatusByDepositSernoAndSecret( String depositSerno,
                                        String secret,
                                        Integer oldStatus,
                                        Integer newStatus);

    Integer updateStatusByDepositSernoAndStatusList( String depositSerno,
                                                    String secret,
                                                    List<Integer> oldStatus,
                                                    Integer newStatus);

    SmbOrder getByDepositSernoAndSecret( String depositSerno, String secret);

    SmbOrder getByOrderNo(String orderNo);

    Integer updateConsignInfoByOrderNo( String orderNo,
                                        Integer oldStatus,
                                        Integer newStatus,
                                        String payChannelCode,
                                        LocalDateTime payTime);

    Integer updateWriteOffStatusByOrderNo( String orderNo,
                                           Integer writeOffStatus,
                                           LocalDateTime writeOffTime);


    /**
     * 默认查询未删除的订单数量
     * @param batchNo
     * @return
     */
    default Integer countByBatchNo(String batchNo){
        return countByBatchNo(batchNo, false);
    }

    /**
     * 根据批次号查询订单数量
     * @param batchNo
     * @param deleted
     * @return
     */
    Integer countByBatchNo(@NonNull String batchNo, @Nullable Boolean deleted);

    /**
     * 根据批次号删除订单
     * @param batchNo
     */
    void deleteByBatchNo(String batchNo);


    /**
     * 获取订单分页列表
     * @return
     */
    Page<SmbOrder> getSmbOrderPage(SmbOrder smbOrder,int pageNum,int pageSize,LocalDateTime startTime,LocalDateTime endTime);


    /**
     * 人工核销
     * @param orderNo 订单号
     * @param smbOrderStatus  核销状态
     */
    void manualWriteOff(String orderNo, Integer smbOrderStatus,String manualRemark);

    /**
     * 订单重试
     * @param orderNo
     */
    void orderRetry(String orderNo);

    /**
     * 导出
     * @param smbOrder
     * @param startTime
     * @param endTime
     * @param response
     */
    void export(SmbOrder smbOrder,LocalDateTime startTime,LocalDateTime endTime,HttpServletResponse response);

    /**
     * 根据存入编号和批次号查询
     * @param depositSerno
     * @param transSerno
     * @return
     */
    SmbOrder getByDepositSernoAndBatchNo(String depositSerno, String transSerno);

    /**
     * 根据存入编号查询（注意：只能用在通过文件批量导入生成的订单，此场景下，depositSerno不可能裂变为多笔订单）
     * @param depositSerno
     * @return
     */
    SmbOrder getOneByDepositSerno(String depositSerno);
}
