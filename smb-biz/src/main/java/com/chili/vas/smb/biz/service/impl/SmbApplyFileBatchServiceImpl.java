package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.common.oss.OSSManager;
import com.chili.vas.smb.biz.dto.SmbApplyFileInfoDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.ext.smb.manager.SmbFileManager;
import com.chili.vas.smb.biz.mapper.SmbApplyFileBatchMapper;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;

import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.stream.Stream;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 苏商申请文件批次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
@Slf4j
@Service
public class SmbApplyFileBatchServiceImpl extends ServiceImpl<SmbApplyFileBatchMapper, SmbApplyFileBatch> implements SmbApplyFileBatchService {

    @Autowired
    private SmbApplyFileBatchMapper smbApplyFileBatchMapper;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private SmbFileManager smbFileManager;

    @Autowired
    private OSSManager ossManager;

    @Override
    public SmbApplyFileBatch getByBatchNo(String batchNo) {
        return smbApplyFileBatchMapper.getByBatchNo(batchNo);
    }

    @Override
    public Integer updateTotalCountByBatchNo(String batchNo, Integer count) {
        return smbApplyFileBatchMapper.updateTotalCountByBatchNo(batchNo, count);
    }

    @Override
    public Integer updateResultFileNameByBatchNo(String batchNo, String resultFileName) {
        return smbApplyFileBatchMapper.updateResultFileNameByBatchNo(batchNo, resultFileName);
    }

    @Override
    public Integer updateResultFilePathByBatchNo(String batchNo, String resultFilePath) {
        return smbApplyFileBatchMapper.updateResultFilePathByBatchNo(batchNo, resultFilePath);
    }

    @Override
    public Integer updateResultFileOssPathByBatchNo(String batchNo, String resultFileOssPath) {
        return smbApplyFileBatchMapper.updateResultFileOssPathByBatchNo(batchNo, resultFileOssPath);
    }

    @Override
    public Integer updateFileOssPathByBatchNo(String batchNo, String fileOssPath) {
        return smbApplyFileBatchMapper.updateFileOssPathByBatchNo(batchNo, fileOssPath);
    }

    @Override
    public SmbApplyFileBatch create(SmbApplyFileInfoDTO smbApplyFileInfoDTO) {
        SmbApplyFileBatch smbApplyFileBatch = smbApplyFileBatchMapper.getBySerialNo(smbApplyFileInfoDTO.getSerialNo());
        if (smbApplyFileBatch != null) {
            throw new BusinessException("渠道流水号已存在");
        }

        smbApplyFileBatch = smbApplyFileBatchMapper.getByFilePath(smbApplyFileInfoDTO.getFilePath());
        if (smbApplyFileBatch != null) {
            throw new BusinessException("当前文件已存在");
        }

        //1.保存批次
        smbApplyFileBatch = new SmbApplyFileBatch();
        smbApplyFileBatch.setBatchNo(idGenerator.nextIdStr());
        smbApplyFileBatch.setSerialNo(smbApplyFileInfoDTO.getSerialNo());
        smbApplyFileBatch.setFileName(smbApplyFileInfoDTO.getFileName());
        smbApplyFileBatch.setFilePath(smbApplyFileInfoDTO.getFilePath());
        smbApplyFileBatch.setTransDate(smbApplyFileInfoDTO.getTransDate());
        save(smbApplyFileBatch);
        try {
            //2.下载文件
            File file = null;
            try {
                file = smbFileManager.downloadFile(smbApplyFileBatch);
                // 上传到oss 并保存到数据库中
                String ossPath = ossManager.upload(file, "store");
                smbApplyFileBatch.setFileOssPath(ossPath);
                updateById(smbApplyFileBatch);
            } catch (Exception e) {
                throw new BusinessException("文件下载失败", e);
            }

            //3.简单校验文件属性
            try (Stream<String> lines = Files.lines(file.toPath(), StandardCharsets.UTF_8)) {
                String firstLine = lines.findFirst()
                        .orElseThrow(() -> new BusinessException("文件为空，无法读取文件头"));
                Long.parseLong(firstLine);
            } catch (NumberFormatException e) {
                throw new BusinessException("文件头订单数量格式错误", e);
            } catch (IOException e1) {
                throw new BusinessException("文件头订单数量格式错误", e1);
            }
        } catch (Exception e) {
            smbApplyFileBatch.setRemark(e.getMessage());
            smbApplyFileBatch.setDeleted(1);
            updateById(smbApplyFileBatch);
            log.warn("download file error or file is invalid, remove it. batchNo: {}", smbApplyFileBatch.getBatchNo());
            throw e;
        }

        return smbApplyFileBatch;
    }

}
