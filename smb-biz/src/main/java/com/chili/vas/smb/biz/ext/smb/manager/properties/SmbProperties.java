package com.chili.vas.smb.biz.ext.smb.manager.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@Data
@ConfigurationProperties(prefix = "config.smb")
public class SmbProperties {

    /**
     * 临时文件目录
     */
    private String tempPath;

    /**
     * 苏商驰丽配置文件路径
     */
    private String propChiliPath;

    /**
     * 苏商珀岩配置文件路径
     */
    private String propPoyanPath;

    /**
     * 场景码
     */
    private String sceneCode = "0402";

    /**
     * 上传文件类型
     */
    private String uploadFileType = "905";

    /**
     * 下载文件类型
     */
    private String downloadFileType = "904";


    private String serviceNum = "6";

}
