package com.chili.vas.smb.biz.job;

import java.util.function.Consumer;

import com.chili.vas.smb.biz.job.context.SystemJobContext;

public interface SystemJob {

    /**
     * 任务编码
     */
    String getJobCode();

    /**
     * 执行任务
     */
    void execute(SystemJobContext context);

    /**
     * 执行任务
     */
    default void execute(SystemJobContext context, Consumer<SystemJobContext> callback){
        execute(context);
        callback.accept(context);
    }

}
