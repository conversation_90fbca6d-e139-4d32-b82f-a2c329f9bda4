package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;

/**
 * SmbBusinessNoticeService 接口
 *
 * <AUTHOR>
 * @since 2025/6/20 14:25
 */
public interface SmbBusinessService {

    /**
     * 通知苏商核销结果
     *
     * @param smbOrder
     * @param smbOrderWriteOffRecord
     * @throws Exception
     */
    void notifyToSmb(SmbOrder smbOrder, SmbOrderWriteOffRecord smbOrderWriteOffRecord);

    /**
     * 订单重试
     * @param smbOrder
     */
    String consign(SmbOrder smbOrder);

    /**
     * 处理寄售
     * @param consignOrderNo
     * @return
     */
    ConsignOrder doConsignDraw(String consignOrderNo);


    void sendQunqiuHttpConsign(ConsignOrder consignOrder);
}
