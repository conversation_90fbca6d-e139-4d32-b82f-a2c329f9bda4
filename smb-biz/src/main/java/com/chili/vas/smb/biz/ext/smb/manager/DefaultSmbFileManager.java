package com.chili.vas.smb.biz.ext.smb.manager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Map;

import org.apache.commons.io.IOUtils;

import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;

public class DefaultSmbFileManager implements SmbFileManager {

    private final SmbProperties smbProperties;
    private final SmbManager smbManager;
    private final SmbApplyFileBatchService smbApplyFileBatchService;

    public DefaultSmbFileManager(SmbProperties smbProperties, SmbManager smbManager, SmbApplyFileBatchService smbApplyFileBatchService) {
        this.smbProperties = smbProperties;
        this.smbManager = smbManager;
        this.smbApplyFileBatchService = smbApplyFileBatchService;
    }

    @Override
    public InputStream downloadStreamByBatchNo(String batchNo) throws Exception {
        SmbApplyFileBatch smbApplyFileBatch = getSmbApplyFileBatchDetailByBatchNo(batchNo);
        return downloadStream(smbApplyFileBatch);
    }

    @Override
    public Map uploadFileByBatchNo(String batchNo) throws Exception {
        SmbApplyFileBatch smbApplyFileBatch = getSmbApplyFileBatchDetailByBatchNo(batchNo);
        return uploadFile(smbApplyFileBatch);
    }

    @Override
    public InputStream downloadStream(SmbApplyFileBatch smbApplyFileBatch) throws Exception {
        return smbManager.downloadStream(IdUtil.simpleUUID(), smbProperties.getSceneCode(), smbProperties.getDownloadFileType(), smbApplyFileBatch.getFilePath());
    }

    @Override
    public Map uploadFile(SmbApplyFileBatch smbApplyFileBatch) throws Exception {
        File file = new File(getLocalFilePath(smbApplyFileBatch.getResultFileName()));
        return smbManager.uploadStream(IdUtil.simpleUUID(), smbProperties.getSceneCode(), file,smbProperties.getUploadFileType());
    }


    /**
     * 通过批次号获取申请文件批次信息
     * @param batchNo
     * @return
     */
    private SmbApplyFileBatch getSmbApplyFileBatchDetailByBatchNo(String batchNo){
        SmbApplyFileBatch smbApplyFileBatch = smbApplyFileBatchService.getByBatchNo(batchNo);
        if (smbApplyFileBatch == null) {
            throw new IllegalArgumentException("can't find smbApplyFileBatch by batchNo: " + batchNo);
        }
        return smbApplyFileBatch;
    }

    @Override
    public File downloadFile(SmbApplyFileBatch smbApplyFileBatch) throws Exception {
        File sdoss = new File(getLocalFilePath(smbApplyFileBatch.getFileName()));
        try(InputStream inputStream =
            smbManager.downloadStream(IdUtil.simpleUUID(), smbProperties.getSceneCode(), smbProperties.getDownloadFileType(), smbApplyFileBatch.getFilePath());
            FileOutputStream fileOutputStream = new FileOutputStream(sdoss)) {
            IOUtils.copy(inputStream, fileOutputStream);
            return sdoss;
        }
    }



    protected String getLocalFilePath(String fileName) {
        if (StrUtil.endWith(smbProperties.getTempPath(), "/")) {
            return smbProperties.getTempPath() + fileName;
        }
        return smbProperties.getTempPath() + "/" + fileName;
    }
}
