package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 寄售订单错误码操作表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 18:23:07
 */
public interface ConsignOrderChannelFailConfigMapper extends BaseMapper<ConsignOrderChannelFailConfig> {

    ConsignOrderChannelFailConfig getByConsignOrderChannelErrorMsgId(@Param("consignOrderChannelErrorMsgId") Long consignOrderChannelErrorMsgId);

    ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorCode(@Param("payChannel")String payChannel,@Param("errorCode") String errorCode);

    ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorMsg(@Param("payChannel") String payChannel,@Param("errorMsg") String errorMsg);

}
