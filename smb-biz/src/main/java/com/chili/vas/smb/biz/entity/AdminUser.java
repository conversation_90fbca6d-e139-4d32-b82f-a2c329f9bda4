package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AdminUser 类
 *
 * <AUTHOR>
 * @since 2025/6/19 10:40
 */
@Data
public class AdminUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识;删除逻辑标识（1：删除   0：不删除）
     */
    @TableLogic
    private Integer deleted;

    /**
     * 姓名
     */
    private String name;
    /**
     *  账号
     */
    private String userAccount;

    /**
     * 密码
     */
    private String pwd;
    /**
     *  性别
     */
    private Integer sex;

    /**
     * 状态;0:禁用  1:正常
     */
    private Boolean enabled;
}
