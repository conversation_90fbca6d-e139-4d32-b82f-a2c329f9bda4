package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.dto.SmbOrderResultDetailDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 苏商申请文件批次详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
public interface SmbApplyFileBatchDetailMapper extends BaseMapper<SmbApplyFileBatchDetail> {

    Integer insertBatch(@Param("smbApplyFileBatchDetails") List<SmbApplyFileBatchDetail> smbApplyFileBatchDetails);

    Integer deleteByBatchNo(@Param("batchNo") String batchNo);

    Integer totalByBatchNo(@Param("batchNo") String batchNo, @Param("resultCode") String resultCode);

    List<SmbApplyFileBatchDetail> pageByBatchNo(@Param("batchNo") String batchNo,
                                                @Param("resultCode") String resultCode,
                                                @Param("offset") Integer offset,
                                                @Param("batchSize") Integer batchSize);


    Integer updateFailDescByBatchNoAndDepositSerno(@Param("code") String code,
                                                   @Param("failDesc") String failDesc,
                                                   @Param("batchNo") String batchNo,
                                                   @Param("depositSernos") List<String> depositSernos);

    List<String> queryRepetitionDepositSerno(@Param("batchNo") String batchNo);

    List<SmbOrderResultDetailDTO> getSmbOrderResultDetailDTOList(@Param("batchNo") String batchNo,
                                                                 @Param("offset") Integer offset,
                                                                 @Param("batchSize") Integer batchSize);

    Integer updateCodeAndFailDescByBatchNoAndDepositSerno(@Param("details") List<SmbApplyFileBatchDetail> details,
                                                          @Param("batchNo") String batchNo);

    /**
     * 更新批次明细状态
     * @param batchNo
     * @param oldCode
     * @param newCode
     */
    Integer updateCodeByBatchNo(@Param("batchNo") String batchNo, @Param("oldCode") String oldCode, @Param("newCode") String newCode);

    /**
     * 当前批次与数据库内订单重复错误
     * @param batchNo
     * @param oldCode
     * @param newCode
     * @param message
     */
    void updateCodeMessageByBatchNo(@Param("batchNo") String batchNo, @Param("oldCode") String oldCode, @Param("newCode") String newCode, @Param("message") String message);

    /**
     * 当前批次内重复数据 更新错误信息
     * @param batchNo
     * @param oldCode
     * @param newCode
     * @param failDesc
     */
    void updateCurrentFileCodeMessageByBatchNo(@Param("batchNo") String batchNo, @Param("oldCode") String oldCode, @Param("newCode") String newCode, @Param("failDesc") String failDesc);
}
