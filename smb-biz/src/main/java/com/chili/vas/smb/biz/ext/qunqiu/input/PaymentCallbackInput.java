package com.chili.vas.smb.biz.ext.qunqiu.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2025/4/25 14:54
 */
@Data
public class PaymentCallbackInput {

    private String orderNo;

    private String serialNo;

    private String payeeAccount;

    private String payeeName;

    private String amount;

    private String platformCode;

    private String payChannel;

    private Integer status;

    private String remark;

    private String retCode;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeFinishDate;

}
