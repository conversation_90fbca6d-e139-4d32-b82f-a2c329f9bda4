package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.JobSetting;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 任务设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
public interface JobSettingMapper extends BaseMapper<JobSetting> {

    /**
     * 根据业务场景编码查询任务设置
     * @param sceneCode 业务场景编码
     * @return 任务设置列表
     */
    List<JobSetting> selectBySceneCode(@Param("sceneCode") String sceneCode);

}
