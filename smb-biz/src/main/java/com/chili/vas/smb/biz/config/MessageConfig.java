package com.chili.vas.smb.biz.config;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.FeishuRobotPlatMessageSender;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MessageConfig 类
 *
 * <AUTHOR>
 * @since 2025/6/13 11:35
 */
@Configuration
@EnableConfigurationProperties(MessageProperties.class)
public class MessageConfig {

    @Bean
    FeishuRobotPlatMessageSender feishuRobotPlatMessageSender(MessageProperties messageProperties, HttpRequestExecutor httpRequestExecutor){
        return new FeishuRobotPlatMessageSender(messageProperties.getFeishu().getRobot(),httpRequestExecutor);
    }
}
