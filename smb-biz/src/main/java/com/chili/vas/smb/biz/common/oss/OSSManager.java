package com.chili.vas.smb.biz.common.oss;


import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSServerManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSSignedManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.InputStream;

@Slf4j
public class OSSManager {

    private final OSSSignedManager ossSignedManager;

    private final OSSServerManager ossServerManager;

    public OSSManager(OSSSignedManager ossSignedManager, OSSServerManager ossServerManager) {
        this.ossSignedManager = ossSignedManager;
        this.ossServerManager = ossServerManager;
    }
    public String upload(File file, String role) {
        //oss上传
        try {
            String md5 = DigestUtil.md5Hex(file);
            FileMeta fileMeta = new DefaultFileMeta(file,md5);
            UploadedFileMeta meta = ossServerManager.save(role, fileMeta, true);
            return meta.getFileName();
        }catch (Exception e){
            log.error("oss upload error",e);
            throw new BusinessException("上传oss失败");
        }

    }

    /**
     * 下载文件到本地
     * @param ossPath
     * @param localFilePath
     */
    public void download(String ossPath,String localFilePath){
        try {
            File file = new File(localFilePath);
            String md5 = DigestUtil.md5Hex(ossPath);
            FileMeta fileMeta = new DefaultFileMeta(new File(ossPath), md5);
            ossServerManager.getInputStream("store", fileMeta, inputStream -> {
                try (InputStream is = inputStream) {
                    FileUtil.writeFromStream(is, file);
                } catch (Exception e) {
                    throw new RuntimeException("写入本地文件失败: " + localFilePath, e);
                }
            });
            if(!file.exists()){
                throw new BusinessException("Oss文件下载到本地失败！");
            }
        }catch (Exception e){
            log.error("oss download error",e);
            throw new BusinessException("Oss文件下载失败: ", e);
        }
    }




    public String url(String ossPath){
        FileMeta fileMeta = new DefaultFileMeta(new File(ossPath), null);
        UploadedFileMeta uploadedFileMeta = ossSignedManager.get("store", fileMeta);
        return String.valueOf(uploadedFileMeta.getUrl());
    }
}
