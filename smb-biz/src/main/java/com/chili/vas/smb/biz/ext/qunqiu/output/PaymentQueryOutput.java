package com.chili.vas.smb.biz.ext.qunqiu.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2025/4/23 14:22
 */
@Data
public class PaymentQueryOutput extends BaseOutput{

    private static String systemErrorCode = "JS1002";
    private static String systemErrorDesc = "订单不存在";

    public boolean isOrderNotExsit(){
        return systemErrorCode.equals(getResultCode()) && systemErrorDesc.equals(getResultMsg());
    }

    private PaymentQueryItem data;

    @Data
    public static class PaymentQueryItem{

        private String orderNo;

        private String serialNo;

        private String payeeAccount;

        private String payeeName;

        private String amount;

        private String platformCode;

        private String payChannel;

        private Integer status;

        private String remark;

        private String retCode;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeDate;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeFinishDate;
    }


}
