package com.chili.vas.smb.biz.vo.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.serializer.rmb.RMBFenToYuanSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ExcelSmbOrderOutput 类
 *
 * <AUTHOR>
 * @since 2025/6/26 14:19
 */
@Data
public class ExcelSmbOrderOutput implements Serializable {

    @ExcelProperty(value = "订单号")
    private String orderNo;

    @ExcelProperty(value = "商户订单号")
    private String depositSerno;

    @ExcelProperty(value = "收款名称")
    private String accountName;


    @ExcelProperty(value = "收款账户")
    private String accountNo;

    @ExcelProperty(value = "开户行号")
    private String bankAccountNum;

    @ExcelProperty(value = "开户行名")
    private String bankAccountName;
    /**
     * 权益金额 单位分
     */
    @ExcelIgnore
    private  Integer equityAmount;

    @ExcelProperty(value = "权益金额(元)")
    private BigDecimal equityAmountYuan;


    /**
     * 客户收益(分)
     */
    @ExcelIgnore
    private Integer customerAmount;

    @ExcelProperty(value = "客户收益(元)")
    private BigDecimal customerAmountYuan;

    @ExcelProperty(value = "卡密")
    private String secret;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "激活时间")
    private LocalDateTime activateTime;


    @ExcelProperty(value = "取消时间")
    private LocalDateTime cancelTime;

    @ExcelProperty(value = "核销时间")
    private LocalDateTime writeOffTime;


    /**
     * 核销状态 0.未核销 1.已核销  2.核销中
     */
    @ExcelIgnore
    private Integer writeOffStatus;

    @ExcelProperty(value = "核销状态")
    private String orderWriteOffStatus;

    /**
     * 状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 12.已取消
     */
    @ExcelIgnore
    private Integer status;
    @ExcelProperty(value = "订单状态")
    private String  orderStatus;


    /**
     * 记录状态
     */
    @ExcelIgnore
    private Integer writeOffRecordStatus;
}
