package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 10:38
 */
@Getter
public enum ConsignOrderManualEnum {

    // 状态 0：否（自动重试）；1：是（自动重试）;
    NO(0, "否"),
    YES(1, "是"),
    ;

    private Integer value;
    private String desc;

    ConsignOrderManualEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
