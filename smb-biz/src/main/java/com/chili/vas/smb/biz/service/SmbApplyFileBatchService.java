package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.dto.SmbApplyFileInfoDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 苏商申请文件批次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
public interface SmbApplyFileBatchService extends IService<SmbApplyFileBatch> {

    /**
     * 创建
     */
    SmbApplyFileBatch create(SmbApplyFileInfoDTO smbApplyFileInfoDTO);

    SmbApplyFileBatch getByBatchNo(String batchNo);

    Integer updateTotalCountByBatchNo(String batchNo, Integer count);

    Integer updateResultFileNameByBatchNo(String batchNo, String resultFileName);

    Integer updateResultFilePathByBatchNo(String batchNo, String resultFilePath);

    Integer updateResultFileOssPathByBatchNo(String batchNo, String resultFileOssPath);

    Integer updateFileOssPathByBatchNo(String batchNo,String fileOssPath);

}
