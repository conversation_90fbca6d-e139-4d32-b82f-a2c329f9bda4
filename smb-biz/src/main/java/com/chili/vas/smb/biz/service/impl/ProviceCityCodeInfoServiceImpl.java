package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.entity.ProviceCityCodeInfo;
import com.chili.vas.smb.biz.mapper.ProviceCityCodeInfoMapper;
import com.chili.vas.smb.biz.service.ProviceCityCodeInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 18:23:07
 */
@Service
public class ProviceCityCodeInfoServiceImpl extends ServiceImpl<ProviceCityCodeInfoMapper, ProviceCityCodeInfo> implements ProviceCityCodeInfoService {

    @Autowired
    ProviceCityCodeInfoMapper proviceCityCodeInfoMapper;

    @Override
    public List<ProviceCityCodeInfo> selectProvinceList() {
        return proviceCityCodeInfoMapper.selectProvinceList();
    }

    @Override
    public List<ProviceCityCodeInfo> selectCityList() {
        return proviceCityCodeInfoMapper.selectCityList();
    }
}
