package com.chili.vas.smb.biz.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.chili.vas.smb.biz.common.constant.ExcelConstants;
import com.chili.vas.smb.biz.common.enums.ConsignOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffRecordStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffStatusEnum;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.mapper.ConsignOrderMapper;
import com.chili.vas.smb.biz.service.ConsignOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chili.vas.smb.biz.util.NumberUtils;
import com.chili.vas.smb.biz.vo.output.ExcelConsignOrderOutput;
import com.chili.vas.smb.biz.vo.output.ExcelSmbOrderOutput;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 寄售订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
@Service
public class ConsignOrderServiceImpl extends ServiceImpl<ConsignOrderMapper, ConsignOrder> implements ConsignOrderService {

    @Autowired
    ConsignOrderMapper consignOrderMapper;

    @Override
    public Integer updateStatusAndConsignInfo(String orderNo, Integer oldStatus, Integer newStatus, String extTradeNo, String payChannel, String retCode, LocalDateTime transactionTime, LocalDateTime transactionFinishTime, String remark) {
        return consignOrderMapper.updateStatusAndConsignInfo(orderNo, oldStatus, newStatus, extTradeNo, payChannel,retCode, transactionTime, transactionFinishTime,remark);
    }

    @Override
    public ConsignOrder getByOrderNo(String orderNo) {
        return consignOrderMapper.getByOrderNo(orderNo);
    }

    @Override
    public Integer updateManualRetryByOrderNo(String orderNo, boolean manualRetry) {
        return consignOrderMapper.updateManualRetryByOrderNo(orderNo, manualRetry);
    }

    @Override
    public List<ConsignOrder> selectWaitHalfHourList() {
        return consignOrderMapper.selectWaitHalfHourList();
    }

    @Override
    public Integer updateStatus(String consignOrderNo, Integer oldStatus, Integer newStatus) {
        return consignOrderMapper.updateStatusByOrderNo(consignOrderNo, oldStatus, newStatus);
    }

    @Override
    public Page<ConsignOrder> getCosignOrderPage(ConsignOrder consignOrder, Integer pageNum, Integer pageSize, LocalDateTime startTime, LocalDateTime endTime) {
        Page<ConsignOrder> page = Page.doSelectPage(pageNum, pageSize, () -> {
               return baseMapper.getCosignOrderList(consignOrder,startTime,endTime);
        });

        return page;
    }

    @Override
    public Page<ConsignOrder> paymentRecord(String smbOrderNo, int pageNum, int pageSize) {
        ConsignOrder consignOrder = new ConsignOrder();
        consignOrder.setSmbOrderNo(smbOrderNo);
        Page<ConsignOrder> page = Page.doSelectPage(pageNum, pageSize, () -> {
            return baseMapper.getCosignOrderList(consignOrder,null,null);
        });
        return page;
    }

    @Override
    public void export(ConsignOrder consignOrder, LocalDateTime startTime, LocalDateTime endTime, HttpServletResponse httpServletResponse) {
        // 后续需要优化为异步导出
        OutputStream outputStream = null;
        //先确定数据总数需要多少个sheet
        Integer count = baseMapper.selectCountByParams(consignOrder, startTime, endTime);
        if (count == null || count == 0){
            throw new BusinessException("暂无数据导出！");
        }
        try {
            //每一个Sheet存放100w条数据
            Integer sheetDataRows = ExcelConstants.PER_SHEET_ROW_COUNT;
            //每次写入的数据5w,每页查询5W
            Integer writeDataRows = ExcelConstants.PER_WRITE_ROW_COUNT;
            //计算需要的Sheet数量
            Integer sheetNum = count % sheetDataRows == 0 ? (count / sheetDataRows) : (count / sheetDataRows + 1);
            //计算一般情况下每一个Sheet需要写入的次数(一般情况不包含最后一个sheet,因为最后一个sheet不确定会写入多少条数据)
            int oneSheetWriteCount = sheetDataRows / writeDataRows;
            //计算最后一个sheet需要写入的次数
            int lastSheetWriteCount = count % sheetDataRows == 0 ? oneSheetWriteCount : (count % sheetDataRows % writeDataRows == 0 ? (count / sheetDataRows / writeDataRows) : (count % sheetDataRows / writeDataRows + 1));
            outputStream = httpServletResponse.getOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            for (int i = 0; i < sheetNum; i++) { // sheet
                WriteSheet writeSheet = EasyExcel.writerSheet(i, "寄售订单数据" + (i + 1)).head(ExcelConsignOrderOutput.class)
                        .build();
                List<ExcelConsignOrderOutput> excelOutputList = new ArrayList<>();
                for (int j = 0; j < (i != sheetNum - 1 ? oneSheetWriteCount : lastSheetWriteCount); j++) {
                    Page<ExcelConsignOrderOutput> page = Page.doSelectPage(j + 1 + oneSheetWriteCount * i, writeDataRows, () -> baseMapper.excelExportQueryList(consignOrder, startTime, endTime));
                    page.getList().forEach(item -> {
                         ConsignOrderStatusEnum consignOrderStatusEnum = ConsignOrderStatusEnum.getConsignOrderStatusEnumByValue(item.getStatus());
                         if (consignOrderStatusEnum != null){
                             item.setOrderStatus(consignOrderStatusEnum.getDesc());
                         }
                         if (item.getAmount() != null){ // 转成元
                             item.setAmountYuan(NumberUtils.fenToYuan(item.getAmount()));
                         }

                    });
                    excelOutputList.addAll(page.getList());
                }
                excelWriter.write(excelOutputList, writeSheet);
            }
            if (sheetNum.equals(0)) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "寄售订单数据0").head(ExcelSmbOrderOutput.class)
                        .build();
                excelWriter.write((Collection<?>) null, writeSheet);
            }

            // 下载EXCEL
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode("寄售订单数据", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            httpServletResponse.setHeader("Content-disposition",
                    "attachment; filename=" + encodedFileName + ".xlsx");
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            throw new BusinessException("导出失败！");
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭流异常！", e);
                }
            }
        }
    }

    @Override
    public ConsignOrder getLastOrderBySmbOrderNo(String smbOrderNo) {
        return consignOrderMapper.getLastOrderBySmbOrderNo(smbOrderNo);
    }


}
