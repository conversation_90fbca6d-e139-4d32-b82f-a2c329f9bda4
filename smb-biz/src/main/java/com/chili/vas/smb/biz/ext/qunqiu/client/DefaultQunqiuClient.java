package com.chili.vas.smb.biz.ext.qunqiu.client;


import org.springframework.scheduling.annotation.Async;

import com.chili.vas.smb.biz.ext.qunqiu.constant.QunqiuConstant;
import com.chili.vas.smb.biz.ext.qunqiu.http.HttpRequestInterceptor;
import com.chili.vas.smb.biz.ext.qunqiu.http.HttpResponseErrorHandler;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentInput;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentQueryInput;
import com.chili.vas.smb.biz.ext.qunqiu.output.PaymentOutput;
import com.chili.vas.smb.biz.ext.qunqiu.output.PaymentQueryOutput;
import com.chili.vas.smb.biz.ext.qunqiu.properties.QunqiuProperties;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultQunqiuClient implements QunqiuClient {


    private final HttpRequestExecutor httpRequestExecutor;
    private final QunqiuProperties qunqiuProperties;
    private final HttpResponseErrorHandler responseErrorHandler;

    public DefaultQunqiuClient(HttpRequestExecutor httpRequestExecutor, QunqiuProperties qunqiuProperties) {
        this.httpRequestExecutor = httpRequestExecutor;
        this.responseErrorHandler = new HttpResponseErrorHandler();
        this.qunqiuProperties = qunqiuProperties;
    }

    @Override
    public PaymentOutput createOrder(PaymentInput paymentInput) {
        String url = qunqiuProperties.getDomain() + QunqiuConstant.Url.CREATE_ORDER;
        paymentInput.setPlatformCode(qunqiuProperties.getPlatformCode());
        HttpRequest<PaymentInput, PaymentOutput> httpRequest = RequestBuilders.<PaymentInput, PaymentOutput>bodyBuilder(url)
                .method(RequestMethod.POST)
                .json(paymentInput)
                .resultType(new DataType<>() {
                })
                .build();
        PaymentOutput result = execute(httpRequest);
        log.info("jisou create order response: {}", JSONObject.getInstance().toJSONString(result));
        return result;
    }

    @Override
    public PaymentQueryOutput queryOrder(PaymentQueryInput paymentQueryInput) {
        String url = qunqiuProperties.getDomain() + QunqiuConstant.Url.QUERY_ORDER;
        paymentQueryInput.setPlatformCode(qunqiuProperties.getPlatformCode());
        HttpRequest<PaymentQueryInput, PaymentQueryOutput> httpRequest = RequestBuilders.<PaymentQueryInput, PaymentQueryOutput>bodyBuilder(url)
                .method(RequestMethod.POST)
                .json(paymentQueryInput)
                .resultType(new DataType<>() {
                })
                .build();
        PaymentQueryOutput result = execute(httpRequest);
        log.info("jisou query order response: {}",JSONObject.getInstance().toJSONString(result));
        return result;
    }


    <P, R> R execute(HttpRequest<P,R> httpRequest){
        HttpRequestInterceptor requestInterceptor = new HttpRequestInterceptor(qunqiuProperties);
        return httpRequestExecutor.execute(httpRequest,null,responseErrorHandler,requestInterceptor,null,null);
    }




}
