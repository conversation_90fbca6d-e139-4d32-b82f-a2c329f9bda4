package com.chili.vas.smb.biz.common.enums;

/**
 * <AUTHOR>
 * @date 2025-08-26
 */
public enum SmbOrderSourceEnum {

    FILE_IMPORT(1, "文件导入"), API_CREATE(2, "API创建");

    private Integer value;
    private String label;

    SmbOrderSourceEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static SmbOrderSourceEnum getEnumByValue(Integer value) {
        for (SmbOrderSourceEnum smbOrderSourceEnum : SmbOrderSourceEnum.values()) {
            if (smbOrderSourceEnum.getValue().equals(value)) {
                return smbOrderSourceEnum;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
}
