package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 寄售订单错误码操作表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 18:23:07
 */
public interface ConsignOrderChannelFailConfigService extends IService<ConsignOrderChannelFailConfig> {

    ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorCode(String payChannel,String errorCode);

    ConsignOrderChannelFailConfig getConsignOrderChannelFailConfigByErrorMsg(String payChannel,String errorMsg);

    ConsignOrderChannelFailConfig getFailConfigByErrorCodeMsg(String payChannel,String errorCode, String errorMsg);

    boolean getManual(String payChannel,String errorCode, String errorMsg);

}
