package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.SmbOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chili.vas.smb.biz.vo.output.ExcelSmbOrderOutput;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 苏商订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
public interface SmbOrderMapper extends BaseMapper<SmbOrder> {

    List<String> queryRepetitionSmbOrderSerno(@Param("depositSernos") List<String> depositSernos);

    Integer insertBatch(@Param("smbOrders") List<SmbOrder> smbOrders);

    Integer updateDeletedByBatchNo(@Param("batchNo") String batchNo, @Param("deleted") Integer deleted);


    Integer updateStatusByDepositSernoAndStatusList(@Param("depositSerno") String depositSerno,
                                                    @Param("secret") String secret,
                                                    @Param("oldStatusList") List<Integer> oldStatus,
                                                    @Param("newStatus") Integer newStatus);

    Integer updateStatusByDepositSernoAndSecret(@Param("depositSerno") String depositSerno,
                                                @Param("secret") String secret,
                                                @Param("oldStatus") Integer oldStatus,
                                                @Param("newStatus") Integer newStatus);

    SmbOrder getByDepositSernoAndSecret(@Param("depositSerno") String depositSerno,
                                         @Param("secret") String secret);

    SmbOrder getByOrderNo(@Param("orderNo") String orderNo);

    Integer updateConsignInfoByOrderNo(@Param("orderNo") String orderNo,
                                       @Param("oldStatus") Integer oldStatus,
                                       @Param("newStatus") Integer newStatus,
                                       @Param("payChannelCode") String payChannelCode,
                                       @Param("payTime") LocalDateTime payTime);

    Integer updateWriteOffStatusByOrderNo(@Param("orderNo") String orderNo,
                                       @Param("writeOffStatus") Integer writeOffStatus,
                                       @Param("writeOffTime") LocalDateTime writeOffTime);

    /**
     * 根据批次号查询订单数量
     * @param batchNo
     * @return
     */
    Integer countByBatchNo(@Param("batchNo") String batchNo, @Param("deleted") Boolean deleted);

    /**
     * 根据批次号删除订单
     * @param batchNo
     */
    void deleteByBatchNo(@Param("batchNo") String batchNo);


    /**+
     * 查询订单列表
     * @param smbOrder
     * @param startTime
     * @param endTime
     * @return
     */
    List<SmbOrder> getSmbOrderList(@Param("smbOrder")SmbOrder smbOrder,@Param("startTime")LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);


    void updateSuccessStatusByOrderNoAndFail(@Param("id") Long id,@Param("failStatus") Integer failStatus,@Param("successStatus") Integer successStatus);

    /**
     * 查询订单数量
     * @param smbOrder
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectCountByParams(@Param("smbOrder")SmbOrder smbOrder,@Param("startTime")LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    /**
     * 导出
     * @param smbOrder
     * @param startTime
     * @param endTime
     * @return
     */
    List<ExcelSmbOrderOutput> excelExportQueryList(@Param("smbOrder")SmbOrder smbOrder,@Param("startTime")LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    /**
     * 根据存入编号和批次号查询
     * @param depositSerno
     * @param transSerno
     * @return
     */
    SmbOrder getByDepositSernoAndBatchNo(@Param("depositSerno") String depositSerno, @Param("transSerno") String transSerno);

    /**
     * 根据存入编号查询（注意：只能用在通过文件批量导入生成的订单，此场景下，depositSerno不可能裂变为多笔订单）
     * @param depositSerno
     * @return
     */
    SmbOrder getOneByDepositSerno(@Param("depositSerno") String depositSerno);
}
