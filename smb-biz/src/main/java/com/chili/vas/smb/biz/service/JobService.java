package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;

import io.micrometer.common.lang.NonNull;

import java.util.Collection;
import java.util.List;

import org.springframework.lang.Nullable;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
public interface JobService extends IService<Job> {

    /**
     * 初始化任务
     * @param sceneCode 业务场景编码
     * @param bizCode 业务ID
     */
    void initJob(String sceneCode, Long bizCode);

    /**
     * 批量保存任务
     * @param jobs 任务列表
     */
    void saveByBatch(Collection<Job> jobs);

    /**
     * 启动任务
     * @param job 任务
     */
    void setStart(Job job);

    /**
     * 结束任务
     * @param job 任务
     * @param status 状态
     */
    default void setEnd(Job job, JobStatusEnum status) {
        setEnd(job, status, null);
    }

    /**
     * 结束任务
     * @param job 任务
     * @param status 状态
     * @param e 异常
     */
    void setEnd(Job job, JobStatusEnum status, @Nullable Exception e);


    /**
     * 根据业务ID和业务场景编码查询任务列表
     * @param bizId 业务ID
     * @param sceneCode 业务场景编码
     * @param status 状态
     * @return 任务列表
     */
    List<Job> listByBizIdAndSceneCode(@NonNull Long bizId, @NonNull String sceneCode, @Nullable JobStatusEnum status);

    /**
     * 根据业务场景编码和状态查询任务列表
     * @param sceneCode 业务场景编码
     * @param status 状态
     * @return 任务列表
     */
    List<Job> listBySceneCodeAndStatus(@Nullable String sceneCode, @Nullable JobStatusEnum status);

}
