package com.chili.vas.smb.biz.ext.qunqiu.http;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.chili.vas.smb.biz.ext.qunqiu.properties.QunqiuProperties;
import com.wftk.http.client.core.common.body.BodyDataType;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.query.HttpQueries;
import com.wftk.jackson.core.JSONObject;
import com.wftk.signature.builder.DefaultSignBuilder;
import com.wftk.signature.builder.SignBuilder;

import java.util.HashMap;
import java.util.Map;

public class HttpRequestInterceptor implements RequestInterceptor {

    private final String CLIENT_ID = "client_id";
    private final String TIMESTAMP = "timestamp";
    private final String NONCE = "nonce";
    private final String BODY = "body";
    private final String SIGN = "sign";

    private final QunqiuProperties qunqiuProperties;

    public HttpRequestInterceptor(QunqiuProperties qunqiuProperties){
        this.qunqiuProperties = qunqiuProperties;
    }

    @Override
    public <P, R> HttpRequest<P, R> pre(HttpRequest<P, R> httpRequest) {
        SignBuilder signBuilder = new DefaultSignBuilder(qunqiuProperties.getSecret());
        HttpQueries httpQueries = httpRequest.getHttpQueries();
        long currentTimeMillis = System.currentTimeMillis();
        String nonce = IdUtil.getSnowflakeNextIdStr();
        HashMap<String, Object> signMap = new HashMap<>();
        signMap.put(CLIENT_ID, qunqiuProperties.getClientId());
        signMap.put(TIMESTAMP, currentTimeMillis);
        signMap.put(NONCE, nonce);
        signMap.putAll(httpQueries);
        HttpBody<P> httpBody = httpRequest.getHttpBody();
        if (httpBody != null && httpBody.getBody() != null) {
            P body = httpBody.getBody();
            if (httpBody.getDataType() == BodyDataType.JSON) {
                signMap.put(BODY, JSONObject.getInstance().toJSONString(body));
            } else {
                Map<String, Object> beanMaps = BeanUtil.beanToMap(body, false, true);
                signMap.putAll(beanMaps);
            }
        }
        String sign = signBuilder.addParams(signMap).build();
        httpQueries.put(CLIENT_ID, qunqiuProperties.getClientId());
        httpQueries.put(TIMESTAMP, currentTimeMillis);
        httpQueries.put(SIGN, sign);
        httpQueries.put(NONCE,nonce);
        return httpRequest;
    }
}
