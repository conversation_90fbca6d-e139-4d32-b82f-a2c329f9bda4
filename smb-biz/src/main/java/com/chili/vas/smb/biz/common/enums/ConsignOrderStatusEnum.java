package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/6/6 14:32
 */
@Getter
public enum ConsignOrderStatusEnum {

    // 状态 0：待处理；1：处理中; 2：成功；11：失败
    INIT(0, "待处理"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "寄售成功"),
    FAIL(11, "寄售失败"),
    ;

    private Integer value;
    private String desc;

    ConsignOrderStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ConsignOrderStatusEnum getConsignOrderStatusEnumByValue(Integer value) {
        for (ConsignOrderStatusEnum consignOrderStatusEnum : ConsignOrderStatusEnum.values()) {
            if (consignOrderStatusEnum.getValue().equals(value)) {
                return consignOrderStatusEnum;
            }
        }
        return null;
    }

}
