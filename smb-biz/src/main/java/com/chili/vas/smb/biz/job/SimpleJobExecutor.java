package com.chili.vas.smb.biz.job;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;
import com.chili.vas.smb.biz.job.context.DefaultSystemJobContext;
import com.chili.vas.smb.biz.job.context.SystemJobContext;
import com.chili.vas.smb.biz.service.JobRecordService;
import com.chili.vas.smb.biz.service.JobService;
import com.wftk.common.core.enums.BaseEnum;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * 任务执行器(仅支持串行任务)
 */
@Slf4j
@Component
public class SimpleJobExecutor implements JobExecutor {

    @Autowired
    private JobService jobService;

    @Autowired
    private JobRecordService jobRecordService;

    private final Map<String, SystemJob> systemJobMap;

    public SimpleJobExecutor(List<SystemJob> systemJobs) {
        this.systemJobMap = systemJobs.stream()
        .collect(Collectors.toMap(SystemJob::getJobCode, Function.identity()));
    }

    @Override
    public void execute(Job job) {
        log.info("execute job: {}", job);
        JobStatusEnum jobStatusEnum = BaseEnum.valueOf(job.getStatus(), JobStatusEnum.class);
        if (jobStatusEnum != JobStatusEnum.PENDING) {
            throw new IllegalStateException("invalid job status: " + job.getStatus());
        }
        startJob(job);
    }


    @Override
    public void execute(@Nullable Long bizId, @NonNull String sceneCode) {
        List<Job> jobList = jobService.listByBizIdAndSceneCode(bizId, sceneCode, JobStatusEnum.PENDING);
        log.info("execute job list: {}", jobList);
        for(Job job : jobList){
            startJob(job);
        }
    }

    /**
     * 执行任务
     * @param job
     */
    protected void startJob(Job job) {
        //设置任务开始
        jobService.setStart(job);
        //更新任务记录状态并执行任务
        jobRecordService.updateStatus(job.getId(), JobStatusEnum.PROCESSING);
        SystemJob systemJob = getSystemJob(job.getJobCode());
        SystemJobContext systemJobContext = getSystemContext(job);
        log.info("execute systemJob with context: [{}]", systemJobContext);
        systemJob.execute(systemJobContext, (context) -> {
            log.info("job execute result: [{}]", context);
            Exception exception = context.getExceptionMap().get(job.getJobCode());
            if(exception != null){
                jobService.setEnd(job, JobStatusEnum.FAIL, exception);
            }else{
                jobService.setEnd(job, JobStatusEnum.SUCCESS, exception);
            }
        });
    }

    /**
     * 获取任务
     */
    protected SystemJob getSystemJob(String jobCode) {
        SystemJob systemJob = systemJobMap.get(jobCode);
        if(systemJob == null){
            throw new IllegalArgumentException("invalid job code: " + jobCode);
        }
        return systemJob;
    }

    /**
     * 获取任务上下文
     */
    protected SystemJobContext getSystemContext(Job job) {
        SystemJobContext systemJobContext = new DefaultSystemJobContext(job);
        systemJobContext.addParam("bizId", job.getBizId());
        return systemJobContext;
    }

    @Override
    public void execute(@Nullable String sceneCode, @NonNull JobStatusEnum status) {
        List<Job> jobList = jobService.listBySceneCodeAndStatus(sceneCode, status);
        log.info("execute job list: {}", jobList);
        for(Job job : jobList){
            startJob(job);
        }
    }

}
