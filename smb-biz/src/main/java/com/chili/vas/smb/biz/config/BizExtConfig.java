package com.chili.vas.smb.biz.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;

/**
 * <AUTHOR>
 * @create 2024/4/30 15:34
 */
@Configuration
@ComponentScan(basePackages = "com.chili.vas.smb.biz.ext",
    includeFilters = @ComponentScan.Filter(type = FilterType.ANNOTATION, classes = Configuration.class))
public class BizExtConfig {

}
