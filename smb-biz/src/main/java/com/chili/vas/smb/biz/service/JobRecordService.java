package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;
import com.chili.vas.smb.biz.entity.JobRecord;

import org.springframework.lang.Nullable;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 任务执行记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
public interface JobRecordService extends IService<JobRecord> {

    /**
     * 根据任务创建记录
     * @param job 任务
     * @return 记录
     */
    JobRecord createByJob(Job job);

    /**
     * 更新状态
     * @param jobId 任务ID
     * @param status 状态
     */
    default void updateStatus(Long jobId, JobStatusEnum status){
        updateStatus(jobId, status, null);
    }

    /**
     * 更新状态
     * @param jobId 任务ID
     * @param status 状态
     */
    void updateStatus(Long jobId, JobStatusEnum status, @Nullable Exception exception);

}
