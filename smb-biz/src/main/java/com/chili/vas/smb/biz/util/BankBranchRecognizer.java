package com.chili.vas.smb.biz.util;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.ProviceCityCodeInfo;
import com.chili.vas.smb.biz.service.ProviceCityCodeInfoService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 16:34
 */
@Slf4j
public class BankBranchRecognizer {

    private final ProviceCityCodeInfoService proviceCityCodeInfoService;

    // 城市数据缓存
    private final Map<String, String> cityToProvinceMap = new HashMap<>();
    private final Map<String, Region> provinceMap = new HashMap<>();
    private final Map<String, Region> cityFullNameMap = new HashMap<>();
    private final Set<String> allCityNames = new HashSet<>();
    private final Set<String> allProvinceNames = new HashSet<>();
    private final Map<String, String> specialCityMapping = new HashMap<>();


    public BankBranchRecognizer(ProviceCityCodeInfoService proviceCityCodeInfoService) {
        this.proviceCityCodeInfoService = proviceCityCodeInfoService;

        // 初始化特殊城市映射（解决同名城市问题）
        initializeSpecialMappings();

        // 从数据库加载地区数据
        loadRegionDataFromDB();
    }

    // 初始化特殊映射
    private void initializeSpecialMappings() {
        // 北京
        specialCityMapping.put("东城", "北京市");
        specialCityMapping.put("西城", "北京市");
        specialCityMapping.put("朝阳", "北京市");
        specialCityMapping.put("丰台", "北京市");
        specialCityMapping.put("石景山", "北京市");
        specialCityMapping.put("海淀", "北京市");
        specialCityMapping.put("顺义", "北京市");
        specialCityMapping.put("通州", "北京市");
        specialCityMapping.put("大兴", "北京市");
        specialCityMapping.put("房山", "北京市");
        specialCityMapping.put("门头沟", "北京市");
        specialCityMapping.put("昌平", "北京市");
        specialCityMapping.put("平谷", "北京市");
        specialCityMapping.put("密云", "北京市");
        specialCityMapping.put("怀柔", "北京市");
        specialCityMapping.put("延庆", "北京市");
        specialCityMapping.put("中关村", "北京市");

        // 上海市
        specialCityMapping.put("黄浦", "上海市");
        specialCityMapping.put("徐汇", "上海市");
        specialCityMapping.put("长宁", "上海市");
        specialCityMapping.put("静安", "上海市");
        specialCityMapping.put("普陀", "上海市");
        specialCityMapping.put("虹口", "上海市");
        specialCityMapping.put("杨浦", "上海市");
        specialCityMapping.put("闵行", "上海市");
        specialCityMapping.put("宝山", "上海市");
        specialCityMapping.put("嘉定", "上海市");
        specialCityMapping.put("浦东", "上海市");
        specialCityMapping.put("金山", "上海市");
        specialCityMapping.put("松江", "上海市");
        specialCityMapping.put("青浦", "上海市");
        specialCityMapping.put("奉贤", "上海市");
        specialCityMapping.put("崇明", "上海市");

        // 天津市
        specialCityMapping.put("滨海", "天津市");
        specialCityMapping.put("和平", "天津市");
//        specialCityMapping.put("河东", "天津市");
//        specialCityMapping.put("河西", "天津市");
//        specialCityMapping.put("河北", "天津市");
        specialCityMapping.put("南开", "天津市");
        specialCityMapping.put("红桥", "天津市");
        specialCityMapping.put("东丽", "天津市");
        specialCityMapping.put("西青", "天津市");
        specialCityMapping.put("津南", "天津市");
        specialCityMapping.put("北辰", "天津市");
        specialCityMapping.put("武清", "天津市");
        specialCityMapping.put("宝坻", "天津市");
        specialCityMapping.put("静海", "天津市");
        specialCityMapping.put("宁河", "天津市");
        specialCityMapping.put("蓟州", "天津市");
    }

    // 从数据库加载地区数据
    private void loadRegionDataFromDB() {
        // 加载省份数据
        loadProvinces();

        // 加载城市数据
        loadCities();
    }

    // 加载省份数据
    private void loadProvinces() {
        List<ProviceCityCodeInfo> proviceCityCodeInfos = proviceCityCodeInfoService.selectProvinceList();

        for (ProviceCityCodeInfo proviceCityCodeInfo : proviceCityCodeInfos) {
            String code = proviceCityCodeInfo.getCode();
            String name = proviceCityCodeInfo.getRegion();

            Region region = new Region();
            region.setProvince(name);
            region.setProvinceCode(code);

            // 全称
            provinceMap.put(name, region);

            // 把简称也添加到数据集中
            String shortName = getProvinceShortName(name);
            provinceMap.put(shortName,region);

            // 添加到省份名称集合（全称和简称）
            allProvinceNames.add(name);
            allProvinceNames.add(shortName);
        }

    }

    // 加载城市数据
    private void loadCities() {

        List<ProviceCityCodeInfo> proviceCityCodeInfos = proviceCityCodeInfoService.selectCityList();
        for (ProviceCityCodeInfo proviceCityCodeInfo:proviceCityCodeInfos) {
            String cityCode = proviceCityCodeInfo.getCode();
            String cityName = proviceCityCodeInfo.getRegion();
            String provinceName = proviceCityCodeInfo.getParentRegion();
            String provinceCode = proviceCityCodeInfo.getParentCode();

            Region region = new Region();
            region.setProvince(provinceName);
            region.setProvinceCode(provinceCode);
            region.setCity(cityName);
            region.setCityCode(cityCode);

            // 添加到城市到省份的映射
            cityFullNameMap.put(cityName, region);

            // 添加城市简称
            String cityShortName = getCityShortName(cityName);
            if (!cityShortName.equals(cityName)) {
                cityFullNameMap.put(cityShortName, region);
            }

            // 添加到城市名称集合
            allCityNames.add(cityName);
            allCityNames.add(cityShortName);
        }


    }

    // 获取省份简称
    private String getProvinceShortName(String fullName) {
        if (fullName == null) return "";
        if (fullName.endsWith("省") || fullName.endsWith("市") ) {
            return fullName.substring(0, fullName.length() - 1);
        }
        if (fullName.endsWith("自治区")) {
            return fullName.substring(0, fullName.length() - 3);
        }
        if (fullName.endsWith("特别行政区")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        return fullName;
    }

    // 获取城市简称（去掉"市"后缀）
    private String getCityShortName(String fullName) {
        if (fullName == null) return "";
        if (fullName.endsWith("市")) {
            return fullName.substring(0, fullName.length() - 1);
        }
        if (fullName.endsWith("盟")) {
            return fullName.substring(0, fullName.length() - 1);
        }
        if (fullName.endsWith("林区")) {
            return fullName.substring(0, fullName.length() - 2);
        }
        if (fullName.endsWith("地区") ) {
            return fullName.substring(0, fullName.length() - 2);
        }

        if (fullName.endsWith("黎族自治县")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        if (fullName.endsWith("黎族苗族自治县")) {
            return fullName.substring(0, fullName.length() - 7);
        }


        if (fullName.endsWith("土家族苗族自治州")) {
            return fullName.substring(0, fullName.length() - 8);
        }
        if (fullName.endsWith("藏族羌族自治州")) {
            return fullName.substring(0, fullName.length() - 7);
        }
        if (fullName.endsWith("布依族苗族自治州")) {
            return fullName.substring(0, fullName.length() - 8);
        }
        if (fullName.endsWith("苗族侗族自治州")) {
            return fullName.substring(0, fullName.length() - 7);
        }
        if (fullName.endsWith("哈尼族彝族自治州")) {
            return fullName.substring(0, fullName.length() - 8);
        }
        if (fullName.endsWith("壮族苗族自治州")) {
            return fullName.substring(0, fullName.length() - 7);
        }
        if (fullName.endsWith("傣族景颇族自治州")) {
            return fullName.substring(0, fullName.length() - 8);
        }
        if (fullName.endsWith("蒙古族藏族自治州")) {
            return fullName.substring(0, fullName.length() - 8);
        }
        if (fullName.endsWith("蒙古自治州")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        if (fullName.endsWith("哈萨克自治州")) {
            return fullName.substring(0, fullName.length() - 6);
        }


        if (fullName.endsWith("回族自治州")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        if (fullName.endsWith("傈僳族自治州")) {
            return fullName.substring(0, fullName.length() - 6);
        }
        if (fullName.endsWith("傣族自治州")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        if (fullName.endsWith("白族自治州")) {
            return fullName.substring(0, fullName.length() - 5);
        }
        if (fullName.endsWith("朝鲜族自治州")) {
            return fullName.substring(0, fullName.length() - 6);
        }
        if (fullName.endsWith("藏族自治州")) {
            // 海南会存在重复区域
            if(!"海南藏族自治州".equals(fullName)){
                return fullName.substring(0, fullName.length() - 5);
            }
        }
        if (fullName.endsWith("彝族自治州")) {
            return fullName.substring(0, fullName.length() - 5);
        }

        if (fullName.endsWith("自治州")) {
            return fullName.substring(0, fullName.length() - 3);
        }

        if (fullName.endsWith("县") && !"县".equals(fullName)) {
            return fullName.substring(0, fullName.length() - 1);
        }
        if (fullName.endsWith("区") ) {
            return fullName.substring(0, fullName.length() - 1);
        }

        return fullName;
    }

    // 识别支行所在省份
    public Region recognizeProvince(String branchName) {
        // 匹配省份
        return matchProvince(branchName);
    }

    // 识别支行所在城市
    public Region recognizeCity(String branchName) {
        // 1. 尝试匹配特殊城市
        for (String city : specialCityMapping.keySet()) {
            // 去除本地银行在其他城市的情况
            if (branchName.replace(city + "银行","").contains(city)) {

                // 获取特殊映射城市
                String mappingCity = specialCityMapping.get(city);
                if(StrUtil.isBlank(mappingCity)){
                    return null;
                }
                // 匹配到了再匹配一下省份 确定省份和城市是匹配的
                // 例如 北京有朝阳区 辽宁省有朝阳市
                Region region = matchProvince(branchName);
                Region region1 = cityFullNameMap.get(mappingCity);
                if(region == null){
                    // 如果匹配不到省份就默认排序前面的
                    return region1;
                }
                if(region1 != null && StrUtil.isNotBlank(region1.getProvince())){
                    if(region1.getProvince().equals(region.getProvince())){
                        return region1;
                    }
                }

            }
        }

        // 2. 尝试匹配城市名称（优先长名称匹配）
        List<String> sortedCityNames = new ArrayList<>(allCityNames);
        sortedCityNames.sort((a, b) -> Integer.compare(b.length(), a.length()));

        for (String city : sortedCityNames) {
            // 去除本地银行在其他城市的情况
            if (branchName.replace(city + "银行","").contains(city)) {

                // 匹配到了再匹配一下省份 确定省份和城市是匹配的
                // 例如 北京有朝阳区 辽宁省有朝阳市
                Region region = matchProvince(branchName);
                Region region1 = cityFullNameMap.get(city);
                if(region == null){
                    // 如果匹配不到省份就默认排序前面的
                    return region1;
                }
                if(region1 != null && StrUtil.isNotBlank(region1.getProvince())){
                    if(region1.getProvince().equals(region.getProvince())){
                        return region1;
                    }
                }
            }
        }

        // 3. 使用正则表达式清理后再次尝试
        String cleanName = cleanBranchName(branchName);
        for (String city : sortedCityNames) {
            // 去除本地银行在其他城市的情况
            if (cleanName.replace(city + "银行","").contains(city)) {

                // 匹配到了再匹配一下省份 确定省份和城市是匹配的
                // 例如 北京有朝阳区 辽宁省有朝阳市
                Region region = matchProvince(branchName);
                Region region1 = cityFullNameMap.get(city);
                if(region == null){
                    // 如果匹配不到省份就默认排序前面的
                    return region1;
                }
                if(region1 != null && StrUtil.isNotBlank(region1.getProvince())){
                    if(region1.getProvince().equals(region.getProvince())){
                        return region1;
                    }
                }
            }
        }

        return null;
    }

    // 清理支行名称中的干扰词
    private String cleanBranchName(String branchName) {
        // 移除银行机构相关词
        String cleaned = branchName.replaceAll("(中国|银行|分行|支行|分理处|营业部|股份有限公司|有限责任公司|营业室|营业所|\\d+)", "");
        // 移除特殊字符
        return cleaned.replaceAll("[()（）]", "");
    }

    // 匹配省份
    private Region matchProvince(String branchName) {
        // 1. 尝试匹配省份名称
        for (String province : allProvinceNames) {
            // 去除本地银行在其他城市的情况
            if (branchName.replace(province + "银行","").contains(province)) {

                return provinceMap.get(province);
            }
        }

        // 2. 清理后再次尝试
        String cleanName = cleanBranchName(branchName);
        for (String province : allProvinceNames) {
            if (cleanName.replace(province + "银行","").contains(province)) {
                return provinceMap.get(province);
            }
        }

        return null;
    }

}
