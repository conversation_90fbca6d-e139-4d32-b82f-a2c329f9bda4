package com.chili.vas.smb.biz.config;

import com.chili.vas.smb.biz.common.lock.LockManager;
import com.chili.vas.smb.biz.common.lock.RedissonLockManager;
import com.chili.vas.smb.biz.common.oss.OSSManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSServerManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSSignedManager;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApplicationConfig {



    @Bean
    LockManager lockManager(DLockFactory<?> dLockFactory) {
        return new RedissonLockManager(dLockFactory);
    }


    /**
     * oss 配置
     * @param ossSignedManager
     * @param ossServerManager
     * @return
     */
    @Bean
    OSSManager ossManager(OSSSignedManager ossSignedManager, OSSServerManager ossServerManager){
        return new OSSManager(ossSignedManager, ossServerManager);
    }
}
