package com.chili.vas.smb.biz.ext.qunqiu.client;


import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentInput;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentQueryInput;
import com.chili.vas.smb.biz.ext.qunqiu.output.PaymentOutput;
import com.chili.vas.smb.biz.ext.qunqiu.output.PaymentQueryOutput;

public interface QunqiuClient {


    /**
     * 下单
     *
     * @param paymentInput
     * @return
     */
    PaymentOutput createOrder(PaymentInput paymentInput);

    /**
     * 查询订单
     *
     * @param paymentQueryInput
     * @return
     */
    PaymentQueryOutput queryOrder(PaymentQueryInput paymentQueryInput);




}
