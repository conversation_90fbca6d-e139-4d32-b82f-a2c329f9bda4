package com.chili.vas.smb.biz.common.enums;

import com.wftk.common.core.enums.BaseEnum;

public enum JobStatusEnum implements BaseEnum {
    PENDING(0, "待执行"),
    PROCESSING(1, "执行中"),
    SUCCESS(2, "成功"),
    FAIL(11, "失败"),
    ;

    private Integer value;
    private String desc;

    JobStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.desc;
    }
}
