package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chili.vas.smb.biz.vo.output.ExcelConsignOrderOutput;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 寄售订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
public interface ConsignOrderMapper extends BaseMapper<ConsignOrder> {

    Integer updateStatusAndConsignInfo(@Param("orderNo")String orderNo,
                                       @Param("oldStatus")Integer oldStatus,
                                       @Param("newStatus")Integer newStatus,
                                       @Param("extTradeNo")String extTradeNo,
                                       @Param("payChannel")String payChannel,
                                       @Param("retCode")String retCode,
                                       @Param("transactionTime") LocalDateTime transactionTime,
                                       @Param("transactionFinishTime")LocalDateTime transactionFinishTime,
                                       @Param("remark")String remark);


    ConsignOrder getByOrderNo(@Param("orderNo")String orderNo);

    Integer updateManualRetryByOrderNo(@Param("orderNo")String orderNo,@Param("manualRetry")boolean manualRetry);


    List<ConsignOrder> selectWaitHalfHourList();


    /**
     * 更新状态
     * @param consignOrderNo
     * @param oldStatus
     * @param newStatus
     * @return
     */
    Integer updateStatusByOrderNo(@Param("orderNo")String consignOrderNo, @Param("oldStatus")Integer oldStatus, @Param("newStatus")Integer newStatus);


    List<ConsignOrder>  getCosignOrderList(@Param("consignOrder")ConsignOrder consignOrder,
                                           @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime);

    Integer selectCountByParams(@Param("consignOrder")ConsignOrder consignOrder,
                                @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime);

    List<ExcelConsignOrderOutput> excelExportQueryList(@Param("consignOrder")ConsignOrder consignOrder,
                                                       @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime);


    ConsignOrder getLastOrderBySmbOrderNo(@Param("smbOrderNo")String smbOrderNo);
}
