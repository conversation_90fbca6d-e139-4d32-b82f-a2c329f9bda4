package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/6/24 10:41
 */
@Getter
public enum SmbOrderWriteOffRecordStatusEnum {

    // 0.待核销 1.核销中  2.核销成功  9.核销失败
    WAIT(0, "待核销"),
    PROCESS(1, "核销中"),
    SUCCESS(2, "核销成功"),
    FAIL(9, "核销失败"),

    ;


    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    SmbOrderWriteOffRecordStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
