package com.chili.vas.smb.biz.ext.smb.constant;

/**
 * <AUTHOR>
 * @createDate 2025/5/19 16:51
 */
public interface SmbConstant {

    /**
     * 签名算法
     */
    String ALGORITHM = "SHA256withRSA";

    /**
     * 字符集
     */
    String CHARSET = "UTF-8";

    /**
     * 内容类型
     */
    String CONTENT_TYPE = "application/x-www-form-urlencoded";

    /**
     * 版本号
     */
    String VERSION = "1.0";

    /**
     * 交易编码
     */
    interface TransCode {

        /**
         * 单笔文件下载
         */
        String SINGLE_FILE_DOWNLOAD = "snb.fsofts.remoteFile.download";

        /**
         * 文件流上传
         */
        String FILE_STREAM_UPLOAD = "snb.fsofts.fileStream.upload";

        /**
         * 权益派发
         */
        String DISTRIBUTION_OF_BENEFITS = "snb.finance.fmtss.equity.grant";

        /**
         * 权益派发-boyan
         */
        String POYAN_DISTRIBUTION_OF_BENEFITS = "poyan/snb.finance.fmtss.equity.grant";

        /**
         * 资金到账通知回调
         */
        String NOTIFICATION_OF_FUND_ARRIVAL_CALLBACK = "snb.finance.fmtss.fund.received.callback";

        /**
         * 结果文件通知回调
         */
        String RESULT_FILE_NOTIFICATION_CALLBACK = "snb.finance.fmtss.resulting.file.callback";

        /**
         * 权益状态查询
         */
        String QUERY_OF_EQUITY_STATUS = "snb.finance.fmtss.equity.status.query";

        /**
         * 权益状态查询
         */
        String POYAN_QUERY_OF_EQUITY_STATUS = "poyan/snb.finance.fmtss.equity.status.query";

        /**
         * 权益申请文件通知
         */
        String NOTICE_OF_EQUITY_APPLY = "snb.finance.fmtss.apply.file.notice";


    }

    interface TransType{

        /**
         * 激活
         */
        String ACTIVE = "01";

        /**
         * 寄售
         */
        String CONSIGN = "02";

        /**
         * 取消
         */
        String CANCEL = "03";

        /**
         * 创建并激活
         */
        String CREATE_AND_ACTIVE = "04";

    }

    interface EquityQueryStatus{

        /**
         * 待激活
         */
        String AWAIT_ACTIVE = "00";

        /**
         * 激活
         */
        String ACTIVE = "06";

        /**
         * 寄售
         */
        String CONSIGN = "01";

        /**
         * 发放取消
         */
        String CANCEL = "07";

    }


}
