package com.chili.vas.smb.biz.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.chili.vas.smb.biz.dto.SmbOrderResultDetailDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.chili.vas.smb.biz.mapper.SmbApplyFileBatchDetailMapper;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * <p>
 * 苏商申请文件批次详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
@Service
public class SmbApplyFileBatchDetailServiceImpl extends ServiceImpl<SmbApplyFileBatchDetailMapper, SmbApplyFileBatchDetail> implements SmbApplyFileBatchDetailService {

    @Autowired
    SmbApplyFileBatchDetailMapper smbApplyFileBatchDetailMapper;

    @Override
    public Integer insertBatch(List<SmbApplyFileBatchDetail> smbApplyFileBatchDetails) {
        return smbApplyFileBatchDetailMapper.insertBatch(smbApplyFileBatchDetails);
    }

    @Override
    public Integer deleteByBatchNo(String batchNo) {
        return smbApplyFileBatchDetailMapper.deleteByBatchNo(batchNo);
    }

    @Override
    public Integer totalByBatchNo(@NonNull String batchNo, @Nullable String resultCode) {
        return smbApplyFileBatchDetailMapper.totalByBatchNo(batchNo, resultCode);
    }

    @Override
    public List<SmbApplyFileBatchDetail> pageByBatchNo(String batchNo, Integer offset, Integer batchSize) {
        return smbApplyFileBatchDetailMapper.pageByBatchNo(batchNo, null, offset,batchSize);
    }

    @Override
    public Integer updateFailDescByBatchNoAndDepositSerno(String code, String failDesc, String batchNo, List<String> depositSernos) {
        return smbApplyFileBatchDetailMapper.updateFailDescByBatchNoAndDepositSerno(code, failDesc, batchNo, depositSernos);
    }

    @Override
    public List<String> queryRepetitionDepositSerno(String batchNo) {
        return smbApplyFileBatchDetailMapper.queryRepetitionDepositSerno(batchNo);
    }

    @Override
    public List<SmbOrderResultDetailDTO> getSmbOrderResultDetailDTOList(String batchNo, Integer offset, Integer batchSize) {
        return smbApplyFileBatchDetailMapper.getSmbOrderResultDetailDTOList(batchNo, offset, batchSize);
    }

    @Override
    public void updateCodeAndFailDescByBatchNoAndDepositSerno(List<SmbApplyFileBatchDetail> details, String batchNo) {
        //切割 100条数据存一次
        List<List<SmbApplyFileBatchDetail>> partition = ListUtil.partition(details, 100);
        for (List<SmbApplyFileBatchDetail> details1: partition) {
            smbApplyFileBatchDetailMapper.updateCodeAndFailDescByBatchNoAndDepositSerno(details1,batchNo);
        }
    }

    @Override
    public void updateCodeMessageByBatchNo(String batchNo, String oldCode, String newCode, String message) {
        baseMapper.updateCodeMessageByBatchNo(batchNo, oldCode, newCode, message);
    }


    @Override
    public void updateCurrentFileCodeMessageByBatchNo(String batchNo, String oldCode, String newCode, String failDesc) {
        baseMapper.updateCurrentFileCodeMessageByBatchNo(batchNo, oldCode, newCode, failDesc);
    }

    @Override
    public List<SmbApplyFileBatchDetail> pageByBatchNoAndResultCode(String batchNo, String resultCode, Integer offset,
            Integer batchSize) {
        return smbApplyFileBatchDetailMapper.pageByBatchNo(batchNo,resultCode,offset,batchSize);
    }

    @Override
    public void updateCodeByBatchNo(String batchNo, String oldCode, String newCode) {
        smbApplyFileBatchDetailMapper.updateCodeByBatchNo(batchNo,oldCode,newCode);
    }

}
