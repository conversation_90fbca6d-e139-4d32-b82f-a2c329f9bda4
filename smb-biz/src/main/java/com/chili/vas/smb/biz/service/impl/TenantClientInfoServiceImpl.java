package com.chili.vas.smb.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chili.vas.smb.biz.entity.TenantClientInfo;
import com.chili.vas.smb.biz.mapper.TenantClientInfoMapper;
import com.chili.vas.smb.biz.service.TenantClientInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TenantClientInfoServiceImpl 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:56
 */
@Service
@Slf4j
public class TenantClientInfoServiceImpl extends ServiceImpl<TenantClientInfoMapper, TenantClientInfo> implements TenantClientInfoService {
    @Override
    public TenantClientInfo findOneByClientId(String clientId, Boolean enable) {
        return baseMapper.selectByClintId(clientId,enable);
    }
}
