package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 苏商申请文件批次详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
@TableName("smb_apply_file_batch_detail")
public class SmbApplyFileBatchDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除记号
     */
    @TableLogic
    private Integer deleted;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 苏商存入编号
     */
    private String depositSerno;

    /**
     * 权益金额
     */
    private Integer equityAmount;

    /**
     * 客户收益
     */
    private Integer customerAmount;

    /**
     * 合作方报酬
     */
    private Integer partnerAmount;

    /**
     * 发放时间
     */
    private LocalDate grantDate;

    /**
     * 收款名称
     */
    private String accountName;

    /**
     * 收款账户
     */
    private String accountNo;

    /**
     * 开户行号
     */
    private String bankAccountNum;

    /**
     * 开户行名
     */
    private String bankAccountName;

    /**
     * 编码
     */
    private String code;

    /**
     * 失败描述
     */
    private String failDesc;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getDepositSerno() {
        return depositSerno;
    }

    public void setDepositSerno(String depositSerno) {
        this.depositSerno = depositSerno;
    }

    public Integer getEquityAmount() {
        return equityAmount;
    }

    public void setEquityAmount(Integer equityAmount) {
        this.equityAmount = equityAmount;
    }

    public Integer getCustomerAmount() {
        return customerAmount;
    }

    public void setCustomerAmount(Integer customerAmount) {
        this.customerAmount = customerAmount;
    }

    public Integer getPartnerAmount() {
        return partnerAmount;
    }

    public void setPartnerAmount(Integer partnerAmount) {
        this.partnerAmount = partnerAmount;
    }

    public LocalDate getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(LocalDate grantDate) {
        this.grantDate = grantDate;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBankAccountNum() {
        return bankAccountNum;
    }

    public void setBankAccountNum(String bankAccountNum) {
        this.bankAccountNum = bankAccountNum;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFailDesc() {
        return failDesc;
    }

    public void setFailDesc(String failDesc) {
        this.failDesc = failDesc;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "SmbApplyFileBatchDetail{" +
                "id = " + id +
                ", createTime = " + createTime +
                ", updateTime = " + updateTime +
                ", deleted = " + deleted +
                ", batchNo = " + batchNo +
                ", depositSerno = " + depositSerno +
                ", equityAmount = " + equityAmount +
                ", customerAmount = " + customerAmount +
                ", partnerAmount = " + partnerAmount +
                ", grantDate = " + grantDate +
                ", accountName = " + accountName +
                ", accountNo = " + accountNo +
                ", bankAccountNum = " + bankAccountNum +
                ", bankAccountName = " + bankAccountName +
                ", remark = " + remark +
                ", code = " + code +
                ", failDesc = " + failDesc +
                "}";
    }
}
