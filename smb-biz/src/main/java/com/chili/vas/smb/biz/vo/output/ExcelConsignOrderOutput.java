package com.chili.vas.smb.biz.vo.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.serializer.rmb.RMBFenToYuanSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ExcelConsignOrderOutput 类
 *
 * <AUTHOR>
 * @since 2025/6/26 15:21
 */
@Data
public class ExcelConsignOrderOutput implements Serializable {


    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 苏商订单号
     */
    @ExcelProperty(value = "商户订单号")
    private String smbOrderNo;

    /**
     * 代付人姓名
     */
    @ExcelProperty(value = "收款名称")
    private String name;

    /**
     * 代付人银行卡号
     */
    @ExcelProperty(value = "收款账户")
    private String bankAccountNo;

    /**
     * 寄售金额（分）
     */
    @ExcelIgnore
    private Integer amount;

    @ExcelProperty(value = "收款金额(元)")
    private BigDecimal amountYuan;

    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 寄售完成时间
     */
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transactionFinishTime;

    /**
     * 代付渠道编号。民生、连连、拉卡拉
     */
    @ExcelProperty(value = "支付渠道")
    private String payChannel;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 订单状态 0：待处理；1：处理中; 2：成功；11：失败
     */
    @ExcelIgnore
    private Integer status;
    @ExcelProperty(value = "订单状态")
    private String  orderStatus;
}
