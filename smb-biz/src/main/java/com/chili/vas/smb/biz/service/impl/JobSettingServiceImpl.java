package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.entity.JobSetting;
import com.chili.vas.smb.biz.mapper.JobSettingMapper;
import com.chili.vas.smb.biz.service.JobSettingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
@Service
public class JobSettingServiceImpl extends ServiceImpl<JobSettingMapper, JobSetting> implements JobSettingService {

    @Override
    public List<JobSetting> listBySceneCode(String sceneCode) {
        return baseMapper.selectBySceneCode(sceneCode);
    }

}
