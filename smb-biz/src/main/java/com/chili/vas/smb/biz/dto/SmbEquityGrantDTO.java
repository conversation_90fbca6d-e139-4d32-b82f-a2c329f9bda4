package com.chili.vas.smb.biz.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @createDate 2025/6/10 20:08
 */
@Data
public class SmbEquityGrantDTO {

    /**
     * 苏商存入流水号
     */
    @NotBlank(message = "苏商存入流水号不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String depositSerno;

    /**
     * 发放时间
     */
    @NotNull(message = "发放时间不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private LocalDate grantDate;

    /**
     * 权益码
     */
    @NotBlank(message = "权益码不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class})
    private String equityCode;

    /**
     * 权益金额
     */
    @NotBlank(message = "权益金额不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String equityAmt;

    /**
     * 合作方报酬
     */
    @NotBlank(message = "合作方报酬不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String partnerAmt;

    /**
     * 客户收益金额
     */
    @NotBlank(message = "客户收益金额不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String customAmt;

    /**
     * 交易类型
     */
    @NotBlank(message = "交易类型不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String transType;

    /**
     * 请求流水号
     */
    @NotBlank(message = "请求流水号不能为空", groups = {ActiveGroup.class, ConsignGroup.class, CancelGroup.class, CreateAndActiveGroup.class})
    private String channelSerialNo;

    /**
     * 回款账户
     * 寄售时必填
     */
    @NotBlank(message = "回款账户号不能为空", groups = {ConsignGroup.class, CreateAndActiveGroup.class})
    private String collectAcctNo;

    /**
     * 回款账户名称
     * 寄售时必填
     */
    @NotBlank(message = "回款账户名称不能为空", groups = {ConsignGroup.class, CreateAndActiveGroup.class})
    private String collectAcctName;

    /**
     * 回款账户联行号
     * 寄售时必填
     */
    @NotBlank(message = "回款账户联行号不能为空", groups = {ConsignGroup.class, CreateAndActiveGroup.class})
    private String collectBankCode;

    /**
     * 回款账户开户行名
     * 寄售时必填
     */
    @NotBlank(message = "回款账户开户行名不能为空", groups = {ConsignGroup.class, CreateAndActiveGroup.class})
    private String collectBankName;

    /**
     * 交易流水号
     */
    @NotBlank(message = "交易流水号不能为空", groups = {ConsignGroup.class, CreateAndActiveGroup.class})
    private String transSerno;



    /**
     * 激活必填
     */
    public static interface ActiveGroup {}

    /**
     * 寄售必填
     */
    public static interface ConsignGroup {}

    /**
     * 取消必填
     */
    public static interface CancelGroup {}

    /**
     * 创建并激活必填
     */
    public static interface CreateAndActiveGroup {}

}
