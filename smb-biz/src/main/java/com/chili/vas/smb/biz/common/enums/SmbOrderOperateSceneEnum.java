package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/6/10 20:44
 */
@Getter
public enum SmbOrderOperateSceneEnum {

    // 文件申请  权益派发-激活  权益派发-寄售  群秋回调 群秋定时查询  权益派发-取消
    FILE_APPLY("file_apply", "文件申请"),
    EQUITY_GRANT_ACTIVATE("equity_grant_activate", "权益派发-激活"),
    EQUITY_GRANT_CONSIGN("equity_grant_consign", "权益派发-寄售"),
    QUNQIU_CALLBACK("qunqiu_callback", "群秋回调"),
    QUNQIU_SCHEDULE_QUERY("qunqiu_schedule_query", "群秋定时查询"),
    EQUITY_GRANT_CANCEL("equity_grant_cancel", "权益派发-取消"),
    EQUITY_GRANT_CONSIGN_RETRY("equity_grant_consign_retry", "权益派发-寄售-重试"),

    ;


    private String value;
    private String desc;

    SmbOrderOperateSceneEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
