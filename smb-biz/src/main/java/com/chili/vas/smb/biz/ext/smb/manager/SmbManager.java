package com.chili.vas.smb.biz.ext.smb.manager;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @createDate 2025/5/23 14:27
 */
public interface SmbManager {

    InputStream downloadStream(String serialNo,String sceneCode,String fileType,String remoteFilePath) throws Exception;

    default Map uploadStream(String serialNo, String sceneCode, File file,String fileType) throws Exception {
        return uploadStream(serialNo, sceneCode, file.getName(), new FileInputStream(file), fileType);
    }

    Map uploadStream(String serialNo, String sceneCode, String fileName, InputStream inputStream, String fileType) throws Exception;

    Map fundReceivedCallback(String serialNo, Map<String, Object> playload) throws Exception;

    Map resultFileCallback(String serialNo, Map<String, Object> playload) throws Exception;

    String getChiliChannelId();

    String getChiliAppCode();

    String getChiliMerchantId();


    String getPoyanChannelId();

    String getPoyanAppCode();

    String getPoyanMerchantId();
}
