package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.entity.Job;
import com.chili.vas.smb.biz.entity.JobRecord;
import com.chili.vas.smb.biz.mapper.JobRecordMapper;
import com.chili.vas.smb.biz.service.JobRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;

import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务执行记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
@Service
public class JobRecordServiceImpl extends ServiceImpl<JobRecordMapper, JobRecord> implements JobRecordService {

    @Override
    public JobRecord createByJob(Job job) {
        JobRecord jobRecord = new JobRecord();
        jobRecord.setJobId(job.getId());
        jobRecord.setJobCode(job.getJobCode());
        jobRecord.setStatus(job.getStatus());
        save(jobRecord);
        return jobRecord;
    }

    @Override
    public void updateStatus(Long jobId, JobStatusEnum status, @Nullable Exception exception) {
        JobRecord jobRecord = baseMapper.findLastOneByJobId(jobId);
        if(jobRecord == null){
            throw new IllegalArgumentException("invalid job id: " + jobId);
        }
        jobRecord.setStatus(status.getValue());
        jobRecord.setUpdateTime(LocalDateTime.now());
        jobRecord.setResult(exception == null ? null : exception.getMessage());
        updateById(jobRecord);
    }

}
