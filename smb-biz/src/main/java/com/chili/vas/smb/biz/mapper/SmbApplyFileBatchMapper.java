package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 苏商申请文件批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
public interface SmbApplyFileBatchMapper extends BaseMapper<SmbApplyFileBatch> {

    SmbApplyFileBatch getByBatchNo(@Param("batchNo")String batchNo);

    Integer updateTotalCountByBatchNo(@Param("batchNo")String batchNo, @Param("count")Integer count);

    Integer updateResultFileNameByBatchNo(@Param("batchNo")String batchNo, @Param("resultFileName")String resultFileName);

    Integer updateResultFilePathByBatchNo(@Param("batchNo")String batchNo, @Param("resultFilePath")String resultFilePath);

    Integer updateResultFileOssPathByBatchNo(@Param("batchNo")String batchNo, @Param("resultFileOssPath")String resultFileOssPath);

    Integer updateFileOssPathByBatchNo(@Param("batchNo")String batchNo, @Param("fileOssPath")String fileOssPath);

    /**
     * 通过渠道流水号获取批次
     */
    SmbApplyFileBatch getBySerialNo(@Param("serialNo")String serialNo);

    /**
     * 通过文件路径获取批次
     */
    SmbApplyFileBatch getByFilePath(@Param("filePath")String filePath);

}
