package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 苏商申请文件批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:17
 */
@TableName("smb_apply_file_batch")
public class SmbApplyFileBatch implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除记号
     */
    @TableLogic
    private Integer deleted;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 苏商请求流水号
     */
    private String serialNo;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 我方下载路径
     */
    private String fileOssPath;

    /**
     * 交易时间
     */
    private LocalDate transDate;

    /**
     * 订单数量
     */
    private Integer totalCount;

    /**
     * 结果文件名称
     */
    private String resultFileName;

    /**
     * 结果文件路径
     */
    private String resultFilePath;

    /**
     * 结果文件oss路径
     */
    private String resultFileOssPath;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public LocalDate getTransDate() {
        return transDate;
    }

    public void setTransDate(LocalDate transDate) {
        this.transDate = transDate;
    }

    public String getFileOssPath() {
        return fileOssPath;
    }

    public void setFileOssPath(String fileOssPath) {
        this.fileOssPath = fileOssPath;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getResultFileName() {
        return resultFileName;
    }

    public void setResultFileName(String resultFileName) {
        this.resultFileName = resultFileName;
    }

    public String getResultFilePath() {
        return resultFilePath;
    }

    public void setResultFilePath(String resultFilePath) {
        this.resultFilePath = resultFilePath;
    }

    public String getResultFileOssPath() {
        return resultFileOssPath;
    }

    public void setResultFileOssPath(String resultFileOssPath) {
        this.resultFileOssPath = resultFileOssPath;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "SmbApplyFileBatch{" +
                "id=" + id +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                ", batchNo='" + batchNo + '\'' +
                ", serialNo='" + serialNo + '\'' +
                ", fileName='" + fileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileOssPath='" + fileOssPath + '\'' +
                ", transDate=" + transDate +
                ", totalCount=" + totalCount +
                ", resultFileName='" + resultFileName + '\'' +
                ", resultFilePath='" + resultFilePath + '\'' +
                ", resultFileOssPath='" + resultFileOssPath + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
