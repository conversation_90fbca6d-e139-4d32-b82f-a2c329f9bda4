package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统操作日志
 */
@TableName("system_opt_log")
public class SystemOptLog implements Serializable {


    @Serial
    private static final long serialVersionUID = 7867731798331852340L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 所属模块
     */
    private String module;

    private String ip;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 操作类型
     */
    private String optType;

    /**
     * 请求方法类型
     */
    private String requestMethod;

    /**
     * 参数
     */
    private String params;

    /**
     * 请求路径
     */
    private String uri;

    private String userAgent;

    /**
     * 描述
     */
    private String description;

    /**
     * 操作时间
     */
    private LocalDateTime optTime;

    /**
     * 是否删除：1.是；0.否
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getOptType() {
        return optType;
    }

    public void setOptType(String optType) {
        this.optType = optType;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getOptTime() {
        return optTime;
    }

    public void setOptTime(LocalDateTime optTime) {
        this.optTime = optTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public String toString() {
        return "SysOptLog{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", module='" + module + '\'' +
                ", ip='" + ip + '\'' +
                ", userType=" + userType +
                ", optType='" + optType + '\'' +
                ", requestMethod='" + requestMethod + '\'' +
                ", params='" + params + '\'' +
                ", uri='" + uri + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", description='" + description + '\'' +
                ", optTime=" + optTime +
                ", deleted=" + deleted +
                ", createTime=" + createTime +
                '}';
    }
}
