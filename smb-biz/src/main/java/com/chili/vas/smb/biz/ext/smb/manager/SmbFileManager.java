package com.chili.vas.smb.biz.ext.smb.manager;

import java.io.File;
import java.io.InputStream;
import java.util.Map;

import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;

public interface SmbFileManager {

    /**
     * 下载文件
     */
    InputStream downloadStreamByBatchNo(String batchNo) throws Exception;

    /**
     * 下载文件
     */
    InputStream downloadStream(SmbApplyFileBatch smbApplyFileBatch) throws Exception;

    /**
     * 下载文件
     */
    File downloadFile(SmbApplyFileBatch smbApplyFileBatch) throws Exception;

    /**
     * 上传文件
     */
    Map uploadFileByBatchNo(String batchNo) throws Exception;

    /**
     * 上传文件
     */
    Map uploadFile(SmbApplyFileBatch smbApplyFileBatch) throws Exception;
}
