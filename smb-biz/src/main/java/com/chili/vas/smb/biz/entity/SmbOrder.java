package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 苏商订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
@TableName("smb_order")
public class SmbOrder implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 苏商存入编号
     */
    private String depositSerno;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 权益金额
     */
    private Integer equityAmount;

    /**
     * 客户收益
     */
    private Integer customerAmount;

    /**
     * 合作方报酬
     */
    private Integer partnerAmount;

    /**
     * 发放时间
     */
    private LocalDate grantDate;

    /**
     * 收款名称
     */
    private String accountName;

    /**
     * 收款账户
     */
    private String accountNo;

    /**
     * 开户行号
     */
    private String bankAccountNum;

    /**
     * 开户行名
     */
    private String bankAccountName;

    /**
     * 卡密
     */
    private String secret;

    /**
     * 状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 12.已取消
     */
    private Integer status;

    /**
     * 支付渠道
     */
    private String payChannelCode;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 激活时间
     */
    private LocalDateTime activateTime;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 核销状态
     */
    private Integer writeOffStatus;

    /**
     * 核销成功时间
     */
    private LocalDateTime writeOffTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除记号
     */
    @TableLogic
    private Integer deleted;

    /**
     * 来源: 1.文件; 2.API
     */
    private Integer source;


    @TableField(exist = false)
    private boolean isWriteOff;

    @TableField(exist = false)
    private boolean orderRetry;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDepositSerno() {
        return depositSerno;
    }

    public void setDepositSerno(String depositSerno) {
        this.depositSerno = depositSerno;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getEquityAmount() {
        return equityAmount;
    }

    public void setEquityAmount(Integer equityAmount) {
        this.equityAmount = equityAmount;
    }

    public Integer getCustomerAmount() {
        return customerAmount;
    }

    public void setCustomerAmount(Integer customerAmount) {
        this.customerAmount = customerAmount;
    }

    public Integer getPartnerAmount() {
        return partnerAmount;
    }

    public void setPartnerAmount(Integer partnerAmount) {
        this.partnerAmount = partnerAmount;
    }

    public LocalDate getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(LocalDate grantDate) {
        this.grantDate = grantDate;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBankAccountNum() {
        return bankAccountNum;
    }

    public void setBankAccountNum(String bankAccountNum) {
        this.bankAccountNum = bankAccountNum;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPayChannelCode() {
        return payChannelCode;
    }

    public void setPayChannelCode(String payChannelCode) {
        this.payChannelCode = payChannelCode;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public LocalDateTime getActivateTime() {
        return activateTime;
    }

    public void setActivateTime(LocalDateTime activateTime) {
        this.activateTime = activateTime;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Integer getWriteOffStatus() {
        return writeOffStatus;
    }

    public void setWriteOffStatus(Integer writeOffStatus) {
        this.writeOffStatus = writeOffStatus;
    }

    public LocalDateTime getWriteOffTime() {
        return writeOffTime;
    }

    public void setWriteOffTime(LocalDateTime writeOffTime) {
        this.writeOffTime = writeOffTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public boolean isWriteOff() {
        return isWriteOff;
    }

    public void setWriteOff(boolean writeOff) {
        isWriteOff = writeOff;
    }

    public boolean isOrderRetry() {
        return orderRetry;
    }

    public void setOrderRetry(boolean orderRetry) {
        this.orderRetry = orderRetry;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "SmbOrder{" +
                "id = " + id +
                ", orderNo = " + orderNo +
                ", depositSerno = " + depositSerno +
                ", batchNo = " + batchNo +
                ", equityAmount = " + equityAmount +
                ", customerAmount = " + customerAmount +
                ", partnerAmount = " + partnerAmount +
                ", grantDate = " + grantDate +
                ", accountName = " + accountName +
                ", accountNo = " + accountNo +
                ", bankAccountNum = " + bankAccountNum +
                ", bankAccountName = " + bankAccountName +
                ", secret = " + secret +
                ", status = " + status +
                ", payChannelCode = " + payChannelCode +
                ", payTime = " + payTime +
                ", activateTime = " + activateTime +
                ", cancelTime = " + cancelTime +
                ", writeOffStatus = " + writeOffStatus +
                ", writeOffTime = " + writeOffTime +
                ", createTime = " + createTime +
                ", updateTime = " + updateTime +
                ", remark = " + remark +
                ", deleted = " + deleted +
                ", source = " + source +
                "}";
    }
}
