package com.chili.vas.smb.biz.ext.qunqiu.http;

import com.chili.vas.smb.biz.ext.qunqiu.exec.HttpServiceException;
import com.wftk.common.core.result.ApiResult;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.handler.response.ResponseErrorHandler;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

public class HttpResponseErrorHandler implements ResponseErrorHandler {
    @Override
    public <P, R> R handle(HttpRequest<P, R> httpRequest, HttpResponse<R> httpResponse) {
        HttpBody<R> httpBody = httpResponse.getHttpBody();
        if (httpBody == null) {
            throw new RuntimeException("body is empty");
        }
        byte[] data = httpBody.getData();
        String jsonStr = new String(data, StandardCharsets.UTF_8);
        Object result = JSONObject.getInstance().parseObject(jsonStr, new TargetType<>() {
            @Override
            public Type getType() {
                return httpRequest.getResultType().getType();
            }
        });
        if (result instanceof ApiResult<?> apiResult) {
            throw new HttpServiceException(apiResult.getCode(),apiResult.getMessage());
        }
        return (R)result;
    }
}
