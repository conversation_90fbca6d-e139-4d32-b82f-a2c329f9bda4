package com.chili.vas.smb.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TenantClientInfo 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:53
 */
@Data
public class TenantClientInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识;删除逻辑标识（1：删除   0：不删除）
     */
    @TableLogic
    private Integer deleted;


    private String clientId;

    private String clientSecret;

    private Boolean enable;

    /**
     * 简介
     */
    private String description;

    /**
     * 预授权类型(多个用逗号分隔)
     */
    private String preGrantType;

    /**
     * 授权类型(多个用逗号分隔)
     */
    private String grantType;

    /**
     * 预授权有效时间
     */
    private Long preAuthExpireInSeconds;

    /**
     * 令牌有效期
     */
    private Long accessTokenExpireInSeconds;

    /**
     * 令牌过渡期（新申请令牌时，老令牌过渡期）
     */
    private Long accessTokenTransitionInSeconds;

}
