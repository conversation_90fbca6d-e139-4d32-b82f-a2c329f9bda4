package com.chili.vas.smb.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chili.vas.smb.biz.entity.AdminUser;
import com.chili.vas.smb.biz.mapper.AdminUserMapper;
import com.chili.vas.smb.biz.service.AdminUserService;
import org.springframework.stereotype.Service;

/**
 * AdminUserServiceImpl 类
 *
 * <AUTHOR>
 * @since 2025/6/19 16:48
 */
@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {
    @Override
    public AdminUser findOneByAccount(String account, Boolean enable) {
        return baseMapper.selectByAccount(account, enable);
    }
}
