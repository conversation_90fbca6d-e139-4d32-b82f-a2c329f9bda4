package com.chili.vas.smb.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;

/**
 * WriteOffRecordService 接口
 *
 * <AUTHOR>
 * @since 2025/6/23 16:10
 */
public interface SmbOrderWriteOffRecordService extends IService<SmbOrderWriteOffRecord> {

    /**
     * 查询未处理完成的核销记录
     * @param smbOrderNo
     * @return
     */
    SmbOrderWriteOffRecord findOneBySmbOrderNo(String smbOrderNo);

    Integer selectCountBySmbOrderNoAndStatus(String smbOrderNo,Integer status);

    Integer updateWriteOffInfoBySmbOrderNo(SmbOrderWriteOffRecord smbOrderWriteOffRecord,Integer oldStatus);

    SmbOrderWriteOffRecord queryLastWriteOffRecordBySmbOrderNo(String smbOrderNo);
}
