package com.chili.vas.smb.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import org.apache.ibatis.annotations.Param;

/**
 * WriteOffRecordMapper 接口
 *
 * <AUTHOR>
 * @since 2025/6/23 16:10
 */
public interface SmbOrderWriteOffRecordMapper extends BaseMapper<SmbOrderWriteOffRecord> {

    /**
     * 查询未处理完成的核销记录
     * @param smbOrderNo
     * @return
     */
    SmbOrderWriteOffRecord findOneBySmbOrderNo(@Param("smbOrderNo")String smbOrderNo);

    Integer selectCountBySmbOrderNoAndStatus(@Param("smbOrderNo")String smbOrderNo,@Param("status")Integer status);

    Integer updateWriteOffInfoBySmbOrderNo(@Param("smbOrderWriteOffRecord") SmbOrderWriteOffRecord smbOrderWriteOffRecord,@Param("oldStatus") Integer oldStatus);


    SmbOrderWriteOffRecord queryLastWriteOffRecordBySmbOrderNo(@Param("smbOrderNo")String smbOrderNo);
}
