package com.chili.vas.smb.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.chili.vas.smb.biz.common.constant.ExcelConstants;
import com.chili.vas.smb.biz.common.constant.JobConstant;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffRecordStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffStatusEnum;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import com.chili.vas.smb.biz.mapper.SmbOrderMapper;
import com.chili.vas.smb.biz.service.JobService;
import com.chili.vas.smb.biz.service.SmbBusinessService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chili.vas.smb.biz.service.SmbOrderWriteOffRecordService;
import com.chili.vas.smb.biz.util.NumberUtils;
import com.chili.vas.smb.biz.vo.output.ExcelSmbOrderOutput;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 苏商订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
@Service
@Slf4j
public class SmbOrderServiceImpl extends ServiceImpl<SmbOrderMapper, SmbOrder> implements SmbOrderService {

    @Autowired
    SmbOrderMapper smbOrderMapper;

    @Autowired
    SmbBusinessService smbBusinessService;

    @Autowired
    SmbOrderWriteOffRecordService smbOrderWriteOffRecordService;

    @Autowired
    JobService jobService;


    @Override
    public List<String> queryRepetitionSmbOrderSerno(List<String> depositSernos) {
        if (CollectionUtil.isEmpty(depositSernos)) {
            return List.of();
        }
        return smbOrderMapper.queryRepetitionSmbOrderSerno(depositSernos);
    }

    @Override
    public Integer insertBatch(List<SmbOrder> smbOrders) {
        return smbOrderMapper.insertBatch(smbOrders);
    }

    @Override
    public Integer updateDeletedByBatchNo(String batchNo, Integer deleted) {
        return smbOrderMapper.updateDeletedByBatchNo(batchNo, deleted);
    }

    @Override
    public Integer updateStatusByDepositSernoAndSecret(String depositSerno, String secret, Integer oldStatus, Integer newStatus) {
        return smbOrderMapper.updateStatusByDepositSernoAndSecret(depositSerno, secret, oldStatus, newStatus);
    }

    @Override
    public Integer updateStatusByDepositSernoAndStatusList(String depositSerno, String secret, List<Integer> oldStatus, Integer newStatus) {
        return smbOrderMapper.updateStatusByDepositSernoAndStatusList(depositSerno, secret, oldStatus, newStatus);
    }

    @Override
    public SmbOrder getByDepositSernoAndSecret(String depositSerno, String secret) {
        return smbOrderMapper.getByDepositSernoAndSecret(depositSerno, secret);
    }

    @Override
    public SmbOrder getByOrderNo(String orderNo) {
        return smbOrderMapper.getByOrderNo(orderNo);
    }

    @Override
    public Integer updateConsignInfoByOrderNo(String orderNo, Integer oldStatus, Integer newStatus, String payChannelCode, LocalDateTime payTime) {
        return smbOrderMapper.updateConsignInfoByOrderNo(orderNo, oldStatus, newStatus, payChannelCode, payTime);
    }

    @Override
    public Integer updateWriteOffStatusByOrderNo(String orderNo, Integer writeOffStatus, LocalDateTime writeOffTime) {
        return smbOrderMapper.updateWriteOffStatusByOrderNo(orderNo, writeOffStatus, writeOffTime);
    }

    @Override
    public Integer countByBatchNo(@NonNull String batchNo, @Nullable Boolean deleted) {
        return smbOrderMapper.countByBatchNo(batchNo, deleted);
    }

    @Override
    public void deleteByBatchNo(String batchNo) {
        smbOrderMapper.deleteByBatchNo(batchNo);
    }

    @Override
    public Page<SmbOrder> getSmbOrderPage(SmbOrder smbOrder, int pageNum, int pageSize, LocalDateTime startTime, LocalDateTime endTime) {
        Page<SmbOrder> page = Page.doSelectPage(pageNum, pageSize, () -> {
            List<SmbOrder> smbOrderList = smbOrderMapper.getSmbOrderList(smbOrder, startTime, endTime);
            for (SmbOrder order : smbOrderList) {
                if (order.getWriteOffStatus() == null) { // 状态为空,默认为未核销
                    order.setWriteOffStatus(SmbOrderWriteOffStatusEnum.WRIETE_OFF_NO.getValue());
                }
                SmbOrderWriteOffRecord smbOrderWriteOffRecord = smbOrderWriteOffRecordService.queryLastWriteOffRecordBySmbOrderNo(order.getOrderNo());
                if (Objects.equals(order.getWriteOffStatus(), SmbOrderWriteOffStatusEnum.WRIETE_OFF_NO.getValue())) {// 待核销
                    if (smbOrderWriteOffRecord != null && (Objects.equals(smbOrderWriteOffRecord.getStatus(), SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue()) ||
                            Objects.equals(smbOrderWriteOffRecord.getStatus(), SmbOrderWriteOffRecordStatusEnum.FAIL.getValue()))) {
                        order.setWriteOffStatus(SmbOrderWriteOffStatusEnum.WRITE_OFF_WAIT.getValue());
                    }
                    if (Objects.equals(order.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue()) && smbOrderWriteOffRecord == null) {// 判断是否显示按钮
                        order.setWriteOff(true);
                    }
                }
                if (smbOrderWriteOffRecord == null
                        && (order.getWriteOffStatus() == null || Objects.equals(order.getWriteOffStatus(), SmbOrderWriteOffStatusEnum.WRIETE_OFF_NO.getValue()))
                        && Objects.equals(order.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())) { // 核销记录为空且,核销记录为空或者待核销,且订单为失败
                    order.setOrderRetry(true);
                }


            }
            return smbOrderList;
        });
        return page;
    }

    @Override
    public void export(SmbOrder smbOrder, LocalDateTime startTime, LocalDateTime endTime, HttpServletResponse httpServletResponse) {
        // 后续需要优化为导出
        OutputStream outputStream = null;
        //先确定数据总数需要多少个sheet
        Integer count = baseMapper.selectCountByParams(smbOrder, startTime, endTime);
        if (count== null || count == 0){
            throw new BusinessException("暂无数据导出！");
        }
        try {
            //每一个Sheet存放100w条数据
            Integer sheetDataRows = ExcelConstants.PER_SHEET_ROW_COUNT;
            //每次写入的数据量5w,每页查询
            Integer writeDataRows = ExcelConstants.PER_WRITE_ROW_COUNT;
            //计算需要的Sheet数量
            Integer sheetNum = count % sheetDataRows == 0 ? (count / sheetDataRows) : (count / sheetDataRows + 1);
            //计算一般情况下每一个Sheet需要写入的次数(一般情况不包含最后一个sheet,因为最后一个sheet不确定会写入多少条数据)
            int oneSheetWriteCount = sheetDataRows / writeDataRows;
            //计算最后一个sheet需要写入的次数
            int lastSheetWriteCount = count % sheetDataRows == 0 ? oneSheetWriteCount : (count % sheetDataRows % writeDataRows == 0 ? (count / sheetDataRows / writeDataRows) : (count % sheetDataRows / writeDataRows + 1));
            outputStream = httpServletResponse.getOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            for (int i = 0; i < sheetNum; i++) { // sheet
                WriteSheet writeSheet = EasyExcel.writerSheet(i, "权益订单数据" + (i + 1)).head(ExcelSmbOrderOutput.class)
                        .build();
                List<ExcelSmbOrderOutput> excelOutputList = new ArrayList<>();
                for (int j = 0; j < (i != sheetNum - 1 ? oneSheetWriteCount : lastSheetWriteCount); j++) {
                    Page<ExcelSmbOrderOutput> page = Page.doSelectPage(j + 1 + oneSheetWriteCount * i, writeDataRows, () -> baseMapper.excelExportQueryList(smbOrder, startTime, endTime));
                    page.getList().forEach(item -> {
                        SmbOrderStatusEnum smbOrderStatusEnum =  SmbOrderStatusEnum.getSmbOrderStatusEnumByValue(item.getStatus());
                        if (smbOrderStatusEnum != null){
                            item.setOrderStatus(smbOrderStatusEnum.getDesc());
                        }
                        if (item.getWriteOffRecordStatus() != null && (Objects.equals(item.getWriteOffRecordStatus(), SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue()) ||
                                Objects.equals(item.getWriteOffRecordStatus(), SmbOrderWriteOffRecordStatusEnum.FAIL.getValue()))) {// 判断是不是核销中
                            item.setWriteOffStatus(SmbOrderWriteOffStatusEnum.WRITE_OFF_WAIT.getValue());
                        }

                        if (item.getWriteOffStatus() == null){
                            item.setWriteOffStatus(SmbOrderWriteOffStatusEnum.WRIETE_OFF_NO.getValue());
                        }

                        SmbOrderWriteOffStatusEnum smbOrderWriteOffStatusEnum = SmbOrderWriteOffStatusEnum.getSmbOrderWriteOffStatusEnumByValue(item.getWriteOffStatus());
                        if (smbOrderWriteOffStatusEnum != null){
                            item.setOrderWriteOffStatus(smbOrderWriteOffStatusEnum.getDesc());
                        }
                        if (item.getEquityAmount() != null){ // 转成元
                            item.setEquityAmountYuan(NumberUtils.fenToYuan(item.getEquityAmount()));
                        }
                        if (item.getCustomerAmount() != null){ // 转成元
                            item.setCustomerAmountYuan(NumberUtils.fenToYuan(item.getCustomerAmount()));
                        }
                    });
                    excelOutputList.addAll(page.getList());
                }
                excelWriter.write(excelOutputList, writeSheet);
            }
            if (sheetNum.equals(0)) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "权益订单数据0").head(ExcelSmbOrderOutput.class)
                        .build();
                excelWriter.write((Collection<?>) null, writeSheet);
            }

            // 下载EXCEL
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode("权益订单数据", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            httpServletResponse.setHeader("Content-disposition",
                    "attachment; filename=" + encodedFileName + ".xlsx");
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            throw new BusinessException("导出失败！");
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭流异常！", e);
                }
            }
        }
    }

    @Transactional
    @Override
    public void manualWriteOff(String orderNo, Integer smbOrderStatus, String manualRemark) {
        // 校验状态
        validateOrderStatus(smbOrderStatus);

        // 修改订单状态
        SmbOrder smbOrder = baseMapper.getByOrderNo(orderNo);
        if (smbOrder == null) {
            log.warn("smb manualWriteOff order is not exist order no is {}", orderNo);
            throw new BusinessException("订单不存在！");
        }
        if (!Objects.equals(SmbOrderStatusEnum.CONSIGN_FAIL.getValue(), smbOrder.getStatus())) {
            throw new BusinessException("当前订单状态不能人工核销！");
        }
        if (smbOrder.getWriteOffStatus() != null || Objects.equals(smbOrder.getWriteOffStatus(), SmbOrderWriteOffStatusEnum.WRITE_OFF_YES.getValue())) {
            throw new BusinessException("订单已核销！");
        }

        SmbOrderWriteOffRecord writeOffRecordInDB = smbOrderWriteOffRecordService.findOneBySmbOrderNo(smbOrder.getOrderNo());
        if (writeOffRecordInDB != null) {
            throw new BusinessException("订单核销中！");
        }

        SmbOrderWriteOffRecord writeOffRecord = new SmbOrderWriteOffRecord();
        writeOffRecord.setSmbOrderNo(orderNo);
        writeOffRecord.setDepositSerno(smbOrder.getDepositSerno());
        writeOffRecord.setManualStatus(smbOrderStatus);
        writeOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.WAIT.getValue());
        writeOffRecord.setManualFlag(true);
        writeOffRecord.setManualRemark(manualRemark);
        smbOrderWriteOffRecordService.save(writeOffRecord);

        // 创建调度任务
        jobService.initJob(JobConstant.SceneCode.SMB_ORDER_WRITE_OFF, smbOrder.getId());

    }


    @Override
    public void orderRetry(String orderNo) {
        SmbOrder smbOrder = baseMapper.getByOrderNo(orderNo);
        if (smbOrder == null) {
            log.warn("smb orderRetry order is not exist order no is {}", orderNo);
            throw new BusinessException("订单不存在！");
        }
        if (!Objects.equals(SmbOrderStatusEnum.CONSIGN_FAIL.getValue(), smbOrder.getStatus())) {
            throw new BusinessException("当前订单状态不能重试！");
        }
        if (smbOrder.getWriteOffStatus() != null && Objects.equals(smbOrder.getWriteOffStatus(), SmbOrderWriteOffStatusEnum.WRITE_OFF_YES.getValue())) {
            throw new BusinessException("当前订单已核销不能重试！");
        }
        SmbOrderWriteOffRecord smbOrderWriteOffRecord = smbOrderWriteOffRecordService.queryLastWriteOffRecordBySmbOrderNo(smbOrder.getOrderNo());
        if (smbOrderWriteOffRecord != null) {
            throw new BusinessException("当前订单已存在发起核销记录,不能发起重试！");
        }
        // 寄售
        String consignOrderNo = smbBusinessService.consign(smbOrder);
        if (StrUtil.isNotBlank(consignOrderNo)) {
            ConsignOrder consignOrder = smbBusinessService.doConsignDraw(consignOrderNo);
            if (consignOrder != null) {
                smbBusinessService.sendQunqiuHttpConsign(consignOrder);
            }
        } else {
            log.info("consignOrderNo is null");
        }
    }


    /**
     * 校验核销通知状态
     *
     * @param smbOrderStatus
     */
    private void validateOrderStatus(Integer smbOrderStatus) {
        SmbOrderStatusEnum smbOrderStatusEnum = SmbOrderStatusEnum.getSmbOrderStatusEnumByValue(smbOrderStatus);
        if (smbOrderStatusEnum == null) {
            throw new BusinessException("核销通知状态错误");
        }
    }

    @Override
    public SmbOrder getByDepositSernoAndBatchNo(String depositSerno, String transSerno) {
        return smbOrderMapper.getByDepositSernoAndBatchNo(depositSerno, transSerno);
    }

    @Override
    public SmbOrder getOneByDepositSerno(String depositSerno) {
        return smbOrderMapper.getOneByDepositSerno(depositSerno);
    }
}
