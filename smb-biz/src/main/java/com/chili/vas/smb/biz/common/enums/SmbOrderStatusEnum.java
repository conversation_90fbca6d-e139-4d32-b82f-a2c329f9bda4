package com.chili.vas.smb.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @createDate 2025/5/28 15:47
 */
@Getter
public enum SmbOrderStatusEnum {

    // 状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 12.已取消
    ACTIVATE_WAITING(0, "待激活"),
    ACTIVATE_COMPLETE(1, "已激活"),
    CONSIGN_PROCESSING(2, "寄售中"),
    CONSIGN_SUCCESS(3, "寄售成功"),
    CONSIGN_FAIL(11, "寄售失败"),
    CANCEL(12, "已取消"),

    ;


    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    SmbOrderStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SmbOrderStatusEnum getSmbOrderStatusEnumByValue(Integer value) {
        for (SmbOrderStatusEnum smbOrderStatusEnum : SmbOrderStatusEnum.values()) {
            if (smbOrderStatusEnum.getValue().equals(value)) {
                return smbOrderStatusEnum;
            }
        }
        return null;
    }



}
