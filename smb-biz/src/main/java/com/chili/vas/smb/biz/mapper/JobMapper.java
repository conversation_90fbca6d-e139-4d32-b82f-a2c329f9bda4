package com.chili.vas.smb.biz.mapper;

import com.chili.vas.smb.biz.entity.Job;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 15:31:30
 */
public interface JobMapper extends BaseMapper<Job> {

    /**
     * 批量保存任务
     * @param jobs 任务列表
     */
    void saveBatch(@Param("jobs") Collection<Job> jobs);

    /**
     * 根据业务ID和业务场景编码查询任务列表
     * @param bizId 业务ID
     * @param sceneCode 业务场景编码
     * @param status 状态
     * @return 任务列表
     */
    List<Job> selectListByBizIdAndSceneCode(@Param("bizId") Long bizId, @Param("sceneCode") String sceneCode, @Param("status") Integer integer);

}
