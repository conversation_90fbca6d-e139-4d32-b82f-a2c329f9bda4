package com.chili.vas.smb.biz.ext.smb.config;

import com.chili.vas.smb.biz.ext.smb.manager.DefaultSmbFileManager;
import com.chili.vas.smb.biz.ext.smb.manager.DefaultSmbManager;
import com.chili.vas.smb.biz.ext.smb.manager.SmbFileManager;
import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.snb.fsos.sdk.SnbSdk;
import com.snb.fsos.sdk.SnbSdkBuilder;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @createDate 2025/5/23 14:20
 */
@Configuration
@EnableConfigurationProperties(SmbProperties.class)
public class SmbConfig {

    @Bean
    @Qualifier("chiliSnbSdk")
    public SnbSdk chiliSnbSdk(SmbProperties smbProperties) throws Exception {
        SnbSdkBuilder snbSdkBuilder = new SnbSdkBuilder();
        snbSdkBuilder.setConfigFile(smbProperties.getPropChiliPath());
        return snbSdkBuilder.build();
    }

    @Bean
    @Qualifier("poyanSnbSdk")
    public SnbSdk poyanSnbSdk(SmbProperties smbProperties) throws Exception {
        SnbSdkBuilder snbSdkBuilder = new SnbSdkBuilder();
        snbSdkBuilder.setConfigFile(smbProperties.getPropPoyanPath());
        return snbSdkBuilder.build();
    }

    @Bean
    public SmbManager smbManager(@Qualifier("chiliSnbSdk") SnbSdk chiliSnbSdk,@Qualifier("poyanSnbSdk") SnbSdk poyanSnbSdk){
        return new DefaultSmbManager(chiliSnbSdk,poyanSnbSdk);
    }

    @Bean
    public SmbFileManager smbFileManager(SmbProperties smbProperties, SmbManager smbManager, SmbApplyFileBatchService smbApplyFileBatchService){
        return new DefaultSmbFileManager(smbProperties, smbManager, smbApplyFileBatchService);
    }

}
