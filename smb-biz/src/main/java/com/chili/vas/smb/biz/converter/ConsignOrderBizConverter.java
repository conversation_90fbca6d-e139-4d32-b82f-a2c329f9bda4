package com.chili.vas.smb.biz.converter;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * ConsignOrderConverter 接口
 *
 * <AUTHOR>
 * @since 2025/6/20 15:24
 */
@Mapper(componentModel = "spring")
public interface ConsignOrderBizConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "orderNo", ignore = true)
    @Mapping(target = "smbOrderNo", source = "orderNo")
    @Mapping(target = "bankAccountNo", source = "accountNo")
    @Mapping(target = "name", source = "accountName")
    @Mapping(target = "amount", source = "customerAmount")
    ConsignOrder toConsignOrder(SmbOrder smbOrder);
}
