package com.chili.vas.smb.biz.service.impl;

import com.chili.vas.smb.biz.service.FeishuMessageService;
import com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot.FeishuRobotPlatMessageType;
import com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot.RenderableFeishuRobotPlatMessage;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.FeishuRobotPlatMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * FeishuMessageServiceImpl 类
 *
 * <AUTHOR>
 * @since 2025/6/13 10:38
 */
@Slf4j
@Service
public class FeishuMessageServiceImpl implements FeishuMessageService {
    @Autowired
    private FeishuRobotPlatMessageSender sender;


    @Override
    public boolean send(String templateCode, Map<String, Object> params) {
        log.info("send feishu message, templateCode: {}, params: {}", templateCode, params);
        RenderableFeishuRobotPlatMessage renderableFeishuRobotPlatMessage = new RenderableFeishuRobotPlatMessage(FeishuRobotPlatMessageType.INTERACTIVE, templateCode);
        renderableFeishuRobotPlatMessage.setParams(params);
        try {
            log.info("feishu send message is :{},{}",templateCode,params);
            return sender.send(renderableFeishuRobotPlatMessage);
        }catch (Exception e){
            log.error("feishu send message error ",e);
            return false;
        }
    }
}
