package com.chili.vas.smb.biz.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * NumberUtils 类
 *
 * <AUTHOR>
 * @since 2025/6/26 16:06
 */
@Slf4j
public class NumberUtils {

    /**
     * 分转为元  四舍五入
     * @param amount 小数位数（建议2位）
     */
    public static BigDecimal fenToYuan(Integer amount) {
        if (amount == null) {
            throw new IllegalArgumentException("金额不能为null");
        }
        return BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }
}
