package com.chili.vas.smb.biz.job.context;

import java.util.Map;

import com.chili.vas.smb.biz.entity.Job;

public interface SystemJobContext {

    /**
     * 任务
     */
    Job getJob();

    /**
     * 添加参数
     */
    SystemJobContext addParam(String key, Object value);

    /**
     * 添加结果
     */
    SystemJobContext addResult(String key, Object value);

    /**
     * 添加异常
     */
    SystemJobContext addException(String key, Exception exception);

    /**
     * 参数
     */
    Map<String, Object> getParamMap();

    /**
     * 结果
     */
    Map<String, Object> getResultMap();

    /**
     * 异常
     */
    Map<String, ? extends Exception> getExceptionMap();

}
