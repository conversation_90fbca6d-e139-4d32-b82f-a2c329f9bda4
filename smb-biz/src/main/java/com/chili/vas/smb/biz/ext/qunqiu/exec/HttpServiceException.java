package com.chili.vas.smb.biz.ext.qunqiu.exec;

import com.wftk.exception.common.ErrorCode;

import java.io.Serial;

public class HttpServiceException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 6734940041182348683L;

    private int code;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public HttpServiceException(int code, String message){
        super(message);
        this.code = code;
    }

    public HttpServiceException(ErrorCode errorCode) {
        super(errorCode.message());
        this.code = errorCode.code();
    }

}
