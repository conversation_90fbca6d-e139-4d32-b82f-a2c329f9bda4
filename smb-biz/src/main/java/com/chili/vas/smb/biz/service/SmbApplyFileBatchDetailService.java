package com.chili.vas.smb.biz.service;

import com.chili.vas.smb.biz.dto.SmbOrderResultDetailDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * <p>
 * 苏商申请文件批次详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22 20:49:18
 */
public interface SmbApplyFileBatchDetailService extends IService<SmbApplyFileBatchDetail> {

    Integer insertBatch(List<SmbApplyFileBatchDetail> smbApplyFileBatchDetails);

    Integer deleteByBatchNo(String batchNo);

    Integer totalByBatchNo(@NonNull String batchNo, @Nullable String resultCode);

    List<SmbApplyFileBatchDetail> pageByBatchNo(String batchNo,
                                                Integer offset,
                                                Integer batchSize);

    List<SmbApplyFileBatchDetail> pageByBatchNoAndResultCode(String batchNo, String resultCode,
                                                             Integer offset,
                                                             Integer batchSize);

    Integer updateFailDescByBatchNoAndDepositSerno(String code,
                                                   String failDesc,
                                                   String batchNo,
                                                   List<String> depositSernos);

    List<String> queryRepetitionDepositSerno(String batchNo);

    List<SmbOrderResultDetailDTO> getSmbOrderResultDetailDTOList(String batchNo,
                                                                 Integer offset,
                                                                 Integer batchSize);

    void updateCodeAndFailDescByBatchNoAndDepositSerno(List<SmbApplyFileBatchDetail> details,
                                                           String batchNo);

    void updateCodeMessageByBatchNo(String batchNo, String oldCode, String newCode, String message);

    /**
     * 更新批次明细状态
     * @param batchNo
     * @param oldCode
     * @param newCode
     * @param failDesc
     */
    void updateCurrentFileCodeMessageByBatchNo(String batchNo, String oldCode, String newCode, String failDesc);

    /**
     * 更新批次明细状态
     * @param batchNo
     * @param oldCode
     * @param newCode
     */
    void updateCodeByBatchNo(String batchNo, String oldCode, String newCode);

}
