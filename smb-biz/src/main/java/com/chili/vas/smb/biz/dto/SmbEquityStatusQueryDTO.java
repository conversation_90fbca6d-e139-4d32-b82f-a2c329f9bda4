package com.chili.vas.smb.biz.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @createDate 2025/6/11 11:12
 */
@Data
public class SmbEquityStatusQueryDTO {

    /**
     * 苏商存入流水号
     */
    @NotBlank(message = "苏商存入流水号不能为空")
    private String depositSerno;

    /**
     * 发放时间
     */
    @NotNull(message = "发放时间不能为空")
    private LocalDate grantDate;

    /**
     * 权益码
     */
    private String equityCode;

    /**
     * 交易流水号
     */
    private String transSerno;
}
