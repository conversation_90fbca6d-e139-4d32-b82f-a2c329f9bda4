package com.chili.vas.smb.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.common.enums.*;
import com.chili.vas.smb.biz.converter.ConsignOrderBizConverter;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderOperateRecord;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import com.chili.vas.smb.biz.ext.qunqiu.client.QunqiuClient;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentInput;
import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
import com.chili.vas.smb.biz.mapper.SmbOrderMapper;
import com.chili.vas.smb.biz.service.*;
import com.chili.vas.smb.biz.util.BankBranchRecognizer;
import com.chili.vas.smb.biz.util.Region;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * SmbBusinessNoticeServiceImpl 类
 *
 * <AUTHOR>
 * @since 2025/6/20 14:26
 */
@Slf4j
@Service
public class SmbBusinessServiceImpl implements SmbBusinessService {

    @Autowired
    SmbManager smbManager;
    @Autowired
    SmbOrderMapper smbOrderMapper;

    @Autowired
    SmbOrderOperateRecordService smbOrderOperateRecordService;
    @Autowired
    ConsignOrderService consignOrderService;

    @Autowired
    IdGenerator idGenerator;

    @Autowired
    ProviceCityCodeInfoService proviceCityCodeInfoService;

    private BankBranchRecognizer bankBranchRecognizer;

    @Autowired
    QunqiuClient qunqiuClient;

    @Autowired
    ConsignOrderBizConverter consignOrderBizConverter;

    @Autowired
    private SmbOrderWriteOffRecordService smbOrderWriteOffRecordService;

    @Autowired
    BankCityCodeInfoService bankCityCodeInfoService;

    @PostConstruct
    public void init() {
        bankBranchRecognizer = new BankBranchRecognizer(proviceCityCodeInfoService);
    }

    @Override
    public void notifyToSmb(SmbOrder smbOrder, SmbOrderWriteOffRecord smbOrderWriteOffRecord) {
        String serilNo = UUID.randomUUID().toString().replace("-", "");
        String transStatus = getTransStatus(smbOrder, smbOrderWriteOffRecord);
        Map payload = buildPayload(smbOrder, transStatus);
        JSONObject jsonObject = JSONObject.getInstance();
        Map respMap;
        try {
            log.info("notifyToSmb request param:{}",jsonObject.toJSONString(payload));
            respMap = smbManager.fundReceivedCallback(serilNo, payload);
        } catch (Exception e) {
            log.error("notifyToSmb orderNo:{} error", smbOrder.getOrderNo(), e);
            SmbOrderWriteOffRecord updateWriteOffRecord = new SmbOrderWriteOffRecord();
            updateWriteOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
            updateWriteOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.FAIL.getValue());
            updateWriteOffRecord.setRequestParam(jsonObject.toJSONString(payload));
            updateWriteOffRecord.setNotifyStatus(transStatus);
            smbOrderWriteOffRecordService.updateWriteOffInfoBySmbOrderNo(updateWriteOffRecord,SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue());
            throw new BusinessException(e);
        }

        // 修改记录字段
        Map respPayload = (Map) respMap.get("payload");
        if (respPayload != null) {
            String rtnCode = (String) respPayload.get("rtnCode");
            if ("0000".equals(rtnCode)) {
                SmbOrderWriteOffRecord updateWriteOffRecord = new SmbOrderWriteOffRecord();
                updateWriteOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
                updateWriteOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.SUCCESS.getValue());
                updateWriteOffRecord.setRequestParam(jsonObject.toJSONString(payload));
                updateWriteOffRecord.setResponseResult(jsonObject.toJSONString(respPayload));
                updateWriteOffRecord.setNotifyStatus(transStatus);
                updateWriteOffRecord.setResultCode(rtnCode);
                smbOrderWriteOffRecordService.updateWriteOffInfoBySmbOrderNo(updateWriteOffRecord,SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue());

                smbOrderMapper.updateWriteOffStatusByOrderNo(smbOrder.getOrderNo(), SmbOrderWriteOffStatusEnum.WRITE_OFF_YES.getValue(), LocalDateTime.now());
            } else {
                SmbOrderWriteOffRecord updateWriteOffRecord = new SmbOrderWriteOffRecord();
                updateWriteOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
                updateWriteOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.FAIL.getValue());
                updateWriteOffRecord.setRequestParam(jsonObject.toJSONString(payload));
                updateWriteOffRecord.setResponseResult(jsonObject.toJSONString(respPayload));
                updateWriteOffRecord.setNotifyStatus(transStatus);
                updateWriteOffRecord.setResultCode(rtnCode);
                smbOrderWriteOffRecordService.updateWriteOffInfoBySmbOrderNo(updateWriteOffRecord,SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue());

                String rtnDesc = (String) respPayload.get("rtnDesc");
                throw new BusinessException("核销失败," + rtnDesc);
            }
        }
    }

    @Override
    public String consign(SmbOrder smbOrder) {
        Integer updated = smbOrderMapper.updateStatusByDepositSernoAndSecret(smbOrder.getDepositSerno(), smbOrder.getSecret(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue());
        if (updated < 1) {
            throw new BusinessException("订单寄售失败");
        }

        // 新增操作记录
        SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
        smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
        smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue());
        smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.EQUITY_GRANT_CONSIGN_RETRY.getValue());
        smbOrderOperateRecordService.save(smbOrderOperateRecord);

        // 查询上一笔寄售订单
        ConsignOrder lastOrder = consignOrderService.getLastOrderBySmbOrderNo(smbOrder.getOrderNo());

        // 创建寄售订单
        ConsignOrder consignOrder = consignOrderBizConverter.toConsignOrder(smbOrder);
        consignOrder.setOrderNo(idGenerator.nextIdStr());
        consignOrder.setStatus(ConsignOrderStatusEnum.INIT.getValue());
        consignOrder.setFlagCard(ConsignOrderFlagCardEnum.PUBLIC.getValue());
        consignOrder.setBankAccountNum(lastOrder.getBankAccountNum());
        consignOrder.setBankAccountName(lastOrder.getBankAccountName());
        consignOrder.setBankAccountNo(lastOrder.getBankAccountNo());
        consignOrder.setName(lastOrder.getName());
        consignOrderService.save(consignOrder);
        return consignOrder.getOrderNo();
    }

    @Override
    public ConsignOrder doConsignDraw(String consignOrderNo) {
        ConsignOrder consignOrder = consignOrderService.getByOrderNo(consignOrderNo);
        if (consignOrder == null) {
            throw new BusinessException("当前寄售订单不存在");
        }

        if (!Objects.equals(consignOrder.getStatus(), ConsignOrderStatusEnum.INIT.getValue())) {
            throw new BusinessException("寄售订单状态异常");
        }

        // 修改为寄售中
        Integer updated = consignOrderService.updateStatus(consignOrderNo, ConsignOrderStatusEnum.INIT.getValue(), ConsignOrderStatusEnum.PROCESSING.getValue());
        if (updated == 0) {
            throw new BusinessException("寄售订单状态修改失败");
        }

        ConsignOrder updateConsignOrder = new ConsignOrder();
        updateConsignOrder.setId(consignOrder.getId());
        // 优先使用精准查询城市编码
        String cityCode = bankCityCodeInfoService.findCityCodeByBraBankName(consignOrder.getBankAccountName());
        if(StrUtil.isBlank(cityCode)){
            Region region = bankBranchRecognizer.recognizeCity(consignOrder.getBankAccountName());
            if (region == null) {
                // 寄售失败
                log.error("city code discern fail. consignOrder: {}", consignOrder);
                // 需要后置校验，并进行人工处理
                return null;
            } else {
                consignOrder.setCityCode(region.getCityCode());
            }
        }else {
            consignOrder.setCityCode(cityCode);
        }

        updateConsignOrder.setCityCode(consignOrder.getCityCode());
        consignOrderService.updateById(updateConsignOrder);

        return consignOrder;
    }

    @Override
    public void sendQunqiuHttpConsign(ConsignOrder consignOrder) {
        // 发起寄售
        PaymentInput paymentInput = new PaymentInput();
        // 转换单位
        String amount = new BigDecimal(consignOrder.getAmount()).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
        paymentInput.setAmount(amount);
        paymentInput.setSerialNo(consignOrder.getOrderNo());
        paymentInput.setPayeeName(consignOrder.getName());
        paymentInput.setPayeeAccount(consignOrder.getBankAccountNo());
        paymentInput.setCityCode(consignOrder.getCityCode());
        paymentInput.setFlagCard(consignOrder.getFlagCard());
        paymentInput.setBrabankName(consignOrder.getBankAccountName());

        qunqiuClient.createOrder(paymentInput);
    }


    private Map buildPayload(SmbOrder smbOrder, String transStatus) {
        // 定义格式 (yyyyMMdd)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map payload = new HashMap<>();
        payload.put("merchantId", smbManager.getPoyanMerchantId());
        payload.put("depositSerno", smbOrder.getDepositSerno());
        payload.put("grantDate", smbOrder.getGrantDate().format(formatter));
        payload.put("equityCode", smbOrder.getSecret());
        payload.put("equityAmt", fenToYuanStr(smbOrder.getEquityAmount()));
        payload.put("partnerAmt", fenToYuanStr(smbOrder.getPartnerAmount()));
        payload.put("customAmt", fenToYuanStr(smbOrder.getCustomerAmount()));
        payload.put("transStatus", transStatus);
        payload.put("partnerRtnCode", "0000");
        payload.put("partnerRtnDesc", "成功");

        return payload;
    }

    private String getTransStatus(SmbOrder smbOrder, SmbOrderWriteOffRecord smbOrderWriteOffRecord) {
        String transStatus;
        if (smbOrderWriteOffRecord.getManualFlag()) {
            //  根据手动核销状态设置状态
            if (Objects.equals(smbOrderWriteOffRecord.getManualStatus(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue())) {
                transStatus = "02";
            } else if (Objects.equals(smbOrderWriteOffRecord.getManualStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())) {
                transStatus = "03";
            } else {
                throw new BusinessException("手动设置状态异常，不能执行回调");
            }
        } else {
            if (Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue())) {
                transStatus = "02";
            } else if (Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())) {
                transStatus = "03";
            } else {
                throw new BusinessException("苏商状态异常，不能执行回调");
            }
        }
        return transStatus;
    }

    private String fenToYuanStr(Integer amt) {
        if (amt == null) {
            return "";
        }
        return new BigDecimal(amt).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
    }
}
