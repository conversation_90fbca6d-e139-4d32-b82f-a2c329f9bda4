package com.chili.vas.smb.biz.ext.qunqiu.output;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/4/23 13:41
 */
@Data
public class BaseOutput {

    private static String successCode = "JS0000";
    private static String successDesc = "成功";

    private static String systemErrorCode = "JS9999";
    private static String systemErrorDesc = "系统异常";


    private String resultCode;

    private String resultMsg;

    public boolean isSuccess(){
        return successCode.equals(resultCode);
    }


    public boolean isSystemError(){
        return systemErrorCode.equals(resultCode);
    }


}
