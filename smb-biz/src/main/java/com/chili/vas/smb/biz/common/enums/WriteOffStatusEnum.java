package com.chili.vas.smb.biz.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * WriteOffStatusEnum 类
 *
 * <AUTHOR>
 * @since 2025/6/23 17:54
 */
public enum WriteOffStatusEnum {

    // 核销状态(0.未核销 1.已核销)
    WRIETE_OFF_NO(3, "核销成功通知"),
    WRITE_OFF_YES(11, "核销失败通知");


    private Integer value;
    private String desc;

    WriteOffStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static List<WriteOffStatusEnum> getSmbOrderStatusList() {
        return Arrays.asList(WriteOffStatusEnum.values());
    }

}
