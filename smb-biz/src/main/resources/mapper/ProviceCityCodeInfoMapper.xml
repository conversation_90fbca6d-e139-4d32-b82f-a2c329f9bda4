<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.ProviceCityCodeInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.ProviceCityCodeInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="code" property="code" />
        <result column="region" property="region" />
        <result column="parent_code" property="parentCode" />
        <result column="type" property="type" />
        <result column="parent_region" property="parentRegion" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, code, region, parent_code,type,parent_region
    </sql>

    <insert id="insertBatch">
        insert into provice_city_code_info
            ( id, create_time, update_time, deleted, code, region, parent_code,type,parent_region)
        values
            <foreach collection="list" item="item" separator=",">
                (
                 #{item.id},#{item.createTime},#{item.updateTime},#{item.deleted},
                 #{item.code},#{item.region},#{item.parentCode},#{item.type},#{item.parentRegion}
                )
            </foreach>
    </insert>

    <select id="selectProvinceList" resultType="com.chili.vas.smb.biz.entity.ProviceCityCodeInfo">
        select * from provice_city_code_info where type = 1
    </select>

    <select id="selectCityList" resultType="com.chili.vas.smb.biz.entity.ProviceCityCodeInfo">
        select * from provice_city_code_info where type = 2
    </select>

</mapper>
