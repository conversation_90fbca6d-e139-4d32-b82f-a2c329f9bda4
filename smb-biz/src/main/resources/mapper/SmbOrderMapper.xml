<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.SmbOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.SmbOrder">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="deposit_serno" property="depositSerno" />
        <result column="batch_no" property="batchNo" />
        <result column="equity_amount" property="equityAmount" />
        <result column="customer_amount" property="customerAmount" />
        <result column="partner_amount" property="partnerAmount" />
        <result column="grant_date" property="grantDate" />
        <result column="account_name" property="accountName" />
        <result column="account_no" property="accountNo" />
        <result column="bank_account_num" property="bankAccountNum" />
        <result column="bank_account_name" property="bankAccountName" />
        <result column="secret" property="secret" />
        <result column="status" property="status" />
        <result column="pay_channel_code" property="payChannelCode" />
        <result column="pay_time" property="payTime" />
        <result column="activate_time" property="activateTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="write_off_status" property="writeOffStatus" />
        <result column="write_off_time" property="writeOffTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, deposit_serno,batch_no, equity_amount, customer_amount, partner_amount, grant_date, account_name, account_no, bank_account_num, bank_account_name, secret, status, pay_channel_code, pay_time, activate_time, cancel_time, write_off_status, write_off_time, create_time, update_time, remark, deleted
    </sql>

    <insert id="insertBatch">
        insert into smb_order
        ( id, order_no, deposit_serno,batch_no, equity_amount, customer_amount, partner_amount, grant_date, account_name, account_no,
         bank_account_num, bank_account_name, secret, status, pay_channel_code, pay_time, activate_time, cancel_time,
         write_off_status, write_off_time, create_time, update_time, remark, deleted)
        values
        <foreach collection="smbOrders" item="smbOrder" separator=",">
            (#{smbOrder.id},#{smbOrder.orderNo},#{smbOrder.depositSerno},#{smbOrder.batchNo},#{smbOrder.equityAmount},#{smbOrder.customerAmount},
            #{smbOrder.partnerAmount},#{smbOrder.grantDate},#{smbOrder.accountName},#{smbOrder.accountNo},#{smbOrder.bankAccountNum},
            #{smbOrder.bankAccountName},#{smbOrder.secret},#{smbOrder.status},#{smbOrder.payChannelCode},#{smbOrder.payTime},
            #{smbOrder.activateTime},#{smbOrder.cancelTime},#{smbOrder.writeOffStatus},#{smbOrder.writeOffTime},#{smbOrder.createTime},
            #{smbOrder.updateTime}, #{smbOrder.remark},#{smbOrder.deleted})
        </foreach>
    </insert>

    <select id="queryRepetitionSmbOrderSerno" resultType="java.lang.String">
        select distinct deposit_serno from smb_order where deleted = 0 and deposit_serno IN
        <foreach collection="depositSernos" item="depositSerno" separator="," open="(" close=")">
            #{depositSerno}
        </foreach>
    </select>

    <update id="updateDeletedByBatchNo">
         update smb_order set deleted = #{deleted} where batch_no = #{batchNo}
    </update>

    <update id="updateStatusByDepositSernoAndStatusList">
        update smb_order set status = #{newStatus}
        where deleted = 0 and deposit_serno = #{depositSerno}
        and secret = #{secret}
        and status in
        <foreach collection="oldStatusList" open="(" close=")" separator="," item="oldStatus">
            #{oldStatus}
        </foreach>
    </update>

    <update id="updateStatusByDepositSernoAndSecret">
        update smb_order set status = #{newStatus}
        where deleted = 0 and deposit_serno = #{depositSerno}
        and status = #{oldStatus}
        and secret = #{secret}
    </update>

    <select id="getByDepositSernoAndSecret" resultType="com.chili.vas.smb.biz.entity.SmbOrder">
        select * from smb_order where deleted = 0 and deposit_serno = #{depositSerno}
        and secret = #{secret}
    </select>

    <select id="getByOrderNo" resultType="com.chili.vas.smb.biz.entity.SmbOrder">
        select * from smb_order where deleted = 0 and order_no = #{orderNo}
    </select>

    <update id="updateConsignInfoByOrderNo">
        update smb_order
        <set>
            status = #{newStatus},
            <if test="payChannelCode != null and payChannelCode != ''">
                pay_channel_code = #{payChannelCode},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
        </set>
        where deleted = 0 and order_no = #{orderNo}
        and status = #{oldStatus}
    </update>

    <update id="updateWriteOffStatusByOrderNo">
        update smb_order set write_off_status = #{writeOffStatus},
                             write_off_time = #{writeOffTime}
        where deleted = 0 and order_no = #{orderNo}
    </update>

    <!-- countByBatchNo --> 

    <select id="countByBatchNo" resultType="java.lang.Integer">
        select count(*) from smb_order 
        <where>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="batchNo != null">
                and batch_no = #{batchNo}
            </if>
        </where>
    </select>

    <!-- deleteByBatchNo --> 

    <delete id="deleteByBatchNo">
        delete from smb_order where batch_no = #{batchNo}
    </delete>



    <select id="getSmbOrderList" resultType="com.chili.vas.smb.biz.entity.SmbOrder">
        SELECT
            *
        FROM
            smb_order so
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus != 1">
                LEFT JOIN smb_order_write_off_record sowor ON so.order_no = sowor.smb_order_no
            </if>
        <where>
            so.deleted = 0
             <if test="smbOrder.orderNo !=null and smbOrder.orderNo != ''">
               and so.order_no =#{smbOrder.orderNo}
             </if>
            <if test="smbOrder.depositSerno != null and smbOrder.depositSerno != ''">
               and  so.deposit_serno =#{smbOrder.depositSerno}
            </if>
            <if test="smbOrder.accountName != null and smbOrder.accountName !=''">
              and  so.account_name =#{smbOrder.accountName}
            </if>
            <if test="smbOrder.status !=null">
              and   so.status = #{smbOrder.status}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 0">
              and (so.write_off_status = 0 OR so.write_off_status IS NULL) and ( (sowor.deleted =0  and sowor.status = 0) OR sowor.id IS NULL )
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 1">
                and so.write_off_status = #{smbOrder.writeOffStatus}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 2">
               and sowor.deleted = 0  AND (sowor.`status` = 1 OR sowor.`status` = 9 )
            </if>
            <if test="startTime != null and endTime != null">
              and  so.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY
         so.create_time DESC
    </select>


    <update id="updateSuccessStatusByOrderNoAndFail">
        UPDATE  smb_order SET status = #{successStatus} WHERE id = #{id} AND status = #{failStatus} and write_off_status =1  AND deleted = 0
    </update>


    <select id="selectCountByParams" resultType="java.lang.Integer">
        SELECT
         COUNT(so.id)
        FROM
        smb_order so
        <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus != 1">
            LEFT JOIN smb_order_write_off_record sowor ON so.order_no = sowor.smb_order_no
        </if>
        <where>
            so.deleted = 0
            <if test="smbOrder.orderNo !=null and smbOrder.orderNo != ''">
                and so.order_no =#{smbOrder.orderNo}
            </if>
            <if test="smbOrder.depositSerno != null and smbOrder.depositSerno != ''">
                and  so.deposit_serno =#{smbOrder.depositSerno}
            </if>
            <if test="smbOrder.accountName != null and smbOrder.accountName !=''">
                and  so.account_name =#{smbOrder.accountName}
            </if>
            <if test="smbOrder.status !=null">
                and   so.status = #{smbOrder.status}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 0">
                and (so.write_off_status = 0 OR so.write_off_status IS NULL) and ( (sowor.deleted =0  and sowor.status = 0) OR sowor.id IS NULL )
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 1">
                and so.write_off_status = #{smbOrder.writeOffStatus}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 2">
                and sowor.deleted = 0  AND (sowor.`status` = 1 OR sowor.`status` = 9 )
            </if>
            <if test="startTime != null and endTime != null">
                and  so.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY
        so.create_time DESC
    </select>


    <select id="excelExportQueryList" resultType="com.chili.vas.smb.biz.vo.output.ExcelSmbOrderOutput">
        SELECT
            so.order_no,
            so.deposit_serno,
            so.account_name,
            so.account_no,
            so.bank_account_num,
            so.bank_account_name,
            so.secret,
            so.remark,
            so.create_time,
            so.activate_time,
            so.equity_amount,
            so.customer_amount,
            so.cancel_time,
            so.write_off_time,
            so.status,
            so.write_off_status,
            sowor.status as writeOffRecordStatus
        FROM
        smb_order so
        LEFT JOIN smb_order_write_off_record sowor ON so.order_no = sowor.smb_order_no
        <where>
            so.deleted = 0
            <if test="smbOrder.orderNo !=null and smbOrder.orderNo != ''">
                and so.order_no =#{smbOrder.orderNo}
            </if>
            <if test="smbOrder.depositSerno != null and smbOrder.depositSerno != ''">
                and  so.deposit_serno =#{smbOrder.depositSerno}
            </if>
            <if test="smbOrder.accountName != null and smbOrder.accountName !=''">
                and  so.account_name =#{smbOrder.accountName}
            </if>
            <if test="smbOrder.status !=null">
                and   so.status = #{smbOrder.status}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 0">
                and (so.write_off_status = 0 OR so.write_off_status IS NULL) and ( (sowor.deleted =0  and sowor.status = 0) OR sowor.id IS NULL )
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 1">
                and so.write_off_status = #{smbOrder.writeOffStatus}
            </if>
            <if test="smbOrder.writeOffStatus !=null and smbOrder.writeOffStatus == 2">
                and sowor.deleted = 0  AND (sowor.`status` = 1 OR sowor.`status` = 9 )
            </if>
            <if test="startTime != null and endTime != null">
                and  so.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY
        so.create_time DESC
    </select>

    <!-- getByDepositSernoAndBatchNo --> 

    <select id="getByDepositSernoAndBatchNo">
        select * from smb_order where deleted = 0 and deposit_serno = #{depositSerno}
        and batch_no = #{batchNo}
    </select>

    <!-- getOneByDepositSerno --> 

    <select id="getOneByDepositSerno">
        select * from smb_order where deleted = 0 and deposit_serno = #{depositSerno} order by id limit 1
    </select>
</mapper>
