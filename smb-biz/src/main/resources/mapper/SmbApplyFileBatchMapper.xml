<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.SmbApplyFileBatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.SmbApplyFileBatch">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="batch_no" property="batchNo" />
        <result column="serial_no" property="serialNo" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_oss_path" property="fileOssPath" />
        <result column="trans_date" property="transDate" />
        <result column="total_count" property="totalCount" />
        <result column="result_file_name" property="resultFileName" />
        <result column="result_file_path" property="resultFilePath" />
        <result column="result_file_oss_path" property="resultFileOssPath" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, batch_no, serial_no,file_name, file_path, file_oss_path,trans_date, total_count,result_file_name, result_file_path, result_file_oss_path, remark
    </sql>

    <select id="getByBatchNo" resultType="com.chili.vas.smb.biz.entity.SmbApplyFileBatch">
        select * from smb_apply_file_batch where deleted = 0 and batch_no = #{batchNo}
    </select>

    <update id="updateTotalCountByBatchNo">
        update smb_apply_file_batch set `total_count` = #{count}  where deleted = 0 and batch_no = #{batchNo}
    </update>

    <update id="updateResultFileNameByBatchNo">
        update smb_apply_file_batch set `result_file_name` = #{resultFileName}  where deleted = 0 and batch_no = #{batchNo}
    </update>

    <update id="updateResultFilePathByBatchNo">
        update smb_apply_file_batch set `result_file_path` = #{resultFilePath}  where deleted = 0 and batch_no = #{batchNo}
    </update>

    <update id="updateResultFileOssPathByBatchNo">
        update smb_apply_file_batch set `result_file_oss_path` = #{resultFileOssPath}  where deleted = 0 and batch_no = #{batchNo}
    </update>

    <update id="updateFileOssPathByBatchNo">
        update smb_apply_file_batch set `file_oss_path` = #{fileOssPath}  where deleted = 0 and batch_no = #{batchNo}
    </update>

    <!-- getBySerialNo --> 

    <select id="getBySerialNo" resultType="com.chili.vas.smb.biz.entity.SmbApplyFileBatch">
        select * from smb_apply_file_batch where deleted = 0 and serial_no = #{serialNo}
    </select>

    <select id="getByFilePath" resultType="com.chili.vas.smb.biz.entity.SmbApplyFileBatch">
        select * from smb_apply_file_batch where deleted = 0 and file_path = #{filePath}
    </select>
</mapper>
