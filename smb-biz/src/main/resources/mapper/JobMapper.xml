<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.JobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.Job">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="biz_id" property="bizId" />
        <result column="scene_code" property="sceneCode" />
        <result column="job_code" property="jobCode" />
        <result column="job_name" property="jobName" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_by, update_by, create_time, update_time, deleted, biz_id, scene_code, job_code, job_name, sort, status, remark, start_time, end_time
    </sql>

    <!-- saveBatch --> 

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO job (id, create_by, update_by, create_time, update_time, deleted, biz_id, scene_code, job_code, job_name, sort, status, remark, start_time, end_time)
        VALUES
        <foreach collection="jobs" item="job" separator=",">
            (#{job.id}, #{job.createBy}, #{job.updateBy}, #{job.createTime}, #{job.updateTime}, #{job.deleted}, #{job.bizId}, #{job.sceneCode}, #{job.jobCode}, #{job.jobName}, #{job.sort}, #{job.status}, #{job.remark}, #{job.startTime}, #{job.endTime})
        </foreach>
    </insert>

    <!-- selectListByBizIdAndSceneCode --> 

    <select id="selectListByBizIdAndSceneCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM job
        <where>
            <if test="bizId != null">
                AND biz_id = #{bizId}
            </if>
            <if test="sceneCode != null and sceneCode != ''">
                AND scene_code = #{sceneCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND deleted = 0
        </where>
        order by update_time,sort
    </select>
</mapper>
