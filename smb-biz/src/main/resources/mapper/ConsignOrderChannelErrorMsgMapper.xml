<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.ConsignOrderChannelErrorMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.ConsignOrderChannelErrorMsg">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="code" property="code" />
        <result column="msg" property="msg" />
        <result column="pay_channel" property="payChannel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, code, msg, pay_channel
    </sql>

</mapper>
