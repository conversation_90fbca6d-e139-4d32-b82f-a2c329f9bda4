<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.ConsignOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.ConsignOrder">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="name" property="name" />
        <result column="bank_account_no" property="bankAccountNo" />
        <result column="amount" property="amount" />
        <result column="smb_order_no" property="smbOrderNo" />
        <result column="remark" property="remark" />
        <result column="pay_channel" property="payChannel" />
        <result column="ret_code" property="retCode" />
        <result column="transaction_time" property="transactionTime" />
        <result column="transaction_finish_time" property="transactionFinishTime" />
        <result column="manual_retry" property="manualRetry" />
        <result column="ext_trade_no" property="extTradeNo" />
        <result column="bank_account_name" property="bankAccountName" />
        <result column="bank_account_num" property="bankAccountNum" />
        <result column="flag_card" property="flagCard" />
        <result column="city_code" property="cityCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, order_no, status, name, bank_account_no, amount, smb_order_no, remark, pay_channel, ret_code, transaction_time, transaction_finish_time, manual_retry, ext_trade_no,bank_account_name,bank_account_num,flag_card,city_code
    </sql>

    <update id="updateStatusAndConsignInfo">
        update consign_order
            <set>
                status = #{newStatus},
                <if test="extTradeNo != null and extTradeNo != ''">
                    ext_trade_no = #{extTradeNo},
                </if>
                <if test="payChannel != null and payChannel != ''">
                    pay_channel = #{payChannel},
                </if>
                <if test="retCode != null and retCode != ''">
                    ret_code = #{retCode},
                </if>
                <if test="transactionTime != null ">
                    transaction_time = #{transactionTime},
                </if>
                <if test="transactionFinishTime != null ">
                    transaction_finish_time = #{transactionFinishTime},
                </if>
                <if test="remark != null and remark != ''">
                    remark = #{remark},
                </if>
            </set>
        where order_no = #{orderNo} and status = #{oldStatus} and deleted = 0
    </update>

    <select id="getByOrderNo" resultType="com.chili.vas.smb.biz.entity.ConsignOrder">
        select * from consign_order where order_no = #{orderNo} and deleted = 0
    </select>

    <update id="updateManualRetryByOrderNo">
        update consign_order set manual_retry = #{manualRetry} where order_no = #{orderNo} and deleted = 0
    </update>

    <select id="selectWaitHalfHourList" resultType="com.chili.vas.smb.biz.entity.ConsignOrder">
        select * from consign_order where deleted = 0 and status = 1 and create_time &lt; DATE_SUB(now(),interval 30 MINUTE)
    </select>

    <!-- updateStatusByOrderNo --> 

    <update id="updateStatusByOrderNo">
        update consign_order set status = #{newStatus} where order_no = #{orderNo} and status = #{oldStatus} and deleted = 0
    </update>

    <select id="getCosignOrderList" resultType="com.chili.vas.smb.biz.entity.ConsignOrder">
        select * from consign_order
        <where>
            deleted = 0
            <if test="consignOrder.orderNo != null and consignOrder.orderNo != ''">
                AND order_no = #{consignOrder.orderNo}
            </if>
            <if test="consignOrder.smbOrderNo != null and consignOrder.smbOrderNo != ''">
                AND smb_order_no = #{consignOrder.smbOrderNo}
            </if>
            <if test="consignOrder.name != null and consignOrder.name != ''">
                AND name = #{consignOrder.name}
            </if>
            <if test="consignOrder.bankAccountNo != null and consignOrder.bankAccountNo != ''">
                AND bank_account_no = #{consignOrder.bankAccountNo}
            </if>
            <if test="consignOrder.status != null">
                AND status = #{consignOrder.status}
            </if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


    <select id="selectCountByParams" resultType="java.lang.Integer">
        select COUNT(*) from consign_order
        <where>
            deleted = 0
            <if test="consignOrder.orderNo != null and consignOrder.orderNo != ''">
                AND order_no = #{consignOrder.orderNo}
            </if>
            <if test="consignOrder.smbOrderNo != null and consignOrder.smbOrderNo != ''">
                AND smb_order_no = #{consignOrder.smbOrderNo}
            </if>
            <if test="consignOrder.name != null and consignOrder.name != ''">
                AND name = #{consignOrder.name}
            </if>
            <if test="consignOrder.bankAccountNo != null and consignOrder.bankAccountNo != ''">
                AND bank_account_no = #{consignOrder.bankAccountNo}
            </if>
            <if test="consignOrder.status != null">
                AND status = #{consignOrder.status}
            </if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="excelExportQueryList" resultType="com.chili.vas.smb.biz.vo.output.ExcelConsignOrderOutput">
        select * from consign_order
        <where>
            deleted = 0
            <if test="consignOrder.orderNo != null and consignOrder.orderNo != ''">
                AND order_no = #{consignOrder.orderNo}
            </if>
            <if test="consignOrder.smbOrderNo != null and consignOrder.smbOrderNo != ''">
                AND smb_order_no = #{consignOrder.smbOrderNo}
            </if>
            <if test="consignOrder.name != null and consignOrder.name != ''">
                AND name = #{consignOrder.name}
            </if>
            <if test="consignOrder.bankAccountNo != null and consignOrder.bankAccountNo != ''">
                AND bank_account_no = #{consignOrder.bankAccountNo}
            </if>
            <if test="consignOrder.status != null">
                AND status = #{consignOrder.status}
            </if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getLastOrderBySmbOrderNo" resultType="com.chili.vas.smb.biz.entity.ConsignOrder">
        select *
        from consign_order
        where smb_order_no = #{smbOrderNo}
          and deleted = 0
        order by create_time desc
        limit 1
    </select>
</mapper>
