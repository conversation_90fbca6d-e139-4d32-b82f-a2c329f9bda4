<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.SmbOrderWriteOffRecordMapper">

    <update id="updateWriteOffInfoBySmbOrderNo">
        update smb_order_write_off_record
        <set>
            status = #{smbOrderWriteOffRecord.status},
            <if test="smbOrderWriteOffRecord.requestParam != null and smbOrderWriteOffRecord.requestParam != ''">
                request_param = #{smbOrderWriteOffRecord.requestParam},
            </if>
            <if test="smbOrderWriteOffRecord.responseResult != null and smbOrderWriteOffRecord.responseResult != ''">
                response_result = #{smbOrderWriteOffRecord.responseResult},
            </if>
            <if test="smbOrderWriteOffRecord.resultCode != null and smbOrderWriteOffRecord.resultCode != ''">
                result_code = #{smbOrderWriteOffRecord.resultCode},
            </if>
            <if test="smbOrderWriteOffRecord.notifyStatus != null and smbOrderWriteOffRecord.notifyStatus != ''">
                notify_status = #{smbOrderWriteOffRecord.notifyStatus},
            </if>
        </set>
        where smb_order_no = #{smbOrderWriteOffRecord.smbOrderNo} and status = #{oldStatus} and deleted = 0
    </update>

    <select id="findOneBySmbOrderNo" resultType="com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord">
        select * from smb_order_write_off_record
                 where smb_order_no = #{smbOrderNo} and deleted = 0

    </select>

    <select id="selectCountBySmbOrderNoAndStatus" resultType="java.lang.Integer">
        select
            count(*)
        from smb_order_write_off_record
        where smb_order_no = #{smbOrderNo}
          and status = #{status} and deleted = 0
    </select>

    <select id="queryLastWriteOffRecordBySmbOrderNo" resultType="com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord">
        SELECT
            *
        FROM
            smb_order_write_off_record WHERE smb_order_no = #{smbOrderNo} AND deleted = 0  ORDER BY create_time DESC LIMIT 1
    </select>

</mapper>
