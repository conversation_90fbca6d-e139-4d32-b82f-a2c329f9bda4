<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.ConsignOrderChannelFailConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="manual" property="manual" />
        <result column="notify" property="notify" />
        <result column="consign_order_channel_error_msg_id" property="consignOrderChannelErrorMsgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, manual, notify, consign_order_channel_error_msg_id
    </sql>

    <select id="getByConsignOrderChannelErrorMsgId" resultType="com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig">
        select * from consign_order_channel_fail_config
                 where consign_order_channel_error_msg_id = #{consignOrderChannelErrorMsgId} and deleted = 0
    </select>

    <select id="getConsignOrderChannelFailConfigByErrorCode"
            resultType="com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig">
        select
            cocfc.*
        from consign_order_channel_fail_config cocfc
            join consign_order_channel_error_msg cocem
                on cocfc.consign_order_channel_error_msg_id = cocem.id
        where cocfc.deleted = 0 and cocem.deleted = 0 and cocem.code = #{errorCode}
          and cocem.pay_channel = #{payChannel}
    </select>

    <select id="getConsignOrderChannelFailConfigByErrorMsg"
            resultType="com.chili.vas.smb.biz.entity.ConsignOrderChannelFailConfig">
        select
            cocfc.*
        from consign_order_channel_fail_config cocfc
            join consign_order_channel_error_msg cocem
                on cocfc.consign_order_channel_error_msg_id = cocem.id
        where cocfc.deleted = 0 and cocem.deleted = 0 and cocem.msg = #{errorMsg}
          and cocem.pay_channel = #{payChannel}
    </select>


</mapper>
