<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.JobSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.JobSetting">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="scene_code" property="sceneCode" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_by, update_by, create_time, update_time, deleted, code, name, scene_code, sort
    </sql>

    <!-- selectBySceneCode --> 

    <select id="selectBySceneCode" resultType="com.chili.vas.smb.biz.entity.JobSetting">
        SELECT
        <include refid="Base_Column_List" />
        FROM job_setting
        WHERE scene_code = #{sceneCode} and deleted = 0
    </select>
</mapper>
