<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.SmbApplyFileBatchDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="batch_no" property="batchNo"/>
        <result column="deposit_serno" property="depositSerno"/>
        <result column="equity_amount" property="equityAmount"/>
        <result column="customer_amount" property="customerAmount"/>
        <result column="partner_amount" property="partnerAmount"/>
        <result column="grant_date" property="grantDate"/>
        <result column="account_name" property="accountName"/>
        <result column="account_no" property="accountNo"/>
        <result column="bank_account_num" property="bankAccountNum"/>
        <result column="bank_account_name" property="bankAccountName"/>
        <result column="code" property="code"/>
        <result column="fail_desc" property="failDesc"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, batch_no, deposit_serno, equity_amount, customer_amount, partner_amount, grant_date, account_name, account_no, bank_account_num, bank_account_name, code,fail_desc,remark
    </sql>

    <insert id="insertBatch">
        insert into smb_apply_file_batch_detail
        (id,create_time, update_time, deleted, batch_no, deposit_serno, equity_amount, customer_amount, partner_amount,
        grant_date, account_name, account_no, bank_account_num, bank_account_name,code,fail_desc,remark)
        values
        <foreach collection="smbApplyFileBatchDetails" item="smbApplyFileBatchDetail" separator=",">
            (#{smbApplyFileBatchDetail.id},#{smbApplyFileBatchDetail.createTime},#{smbApplyFileBatchDetail.updateTime},#{smbApplyFileBatchDetail.deleted},#{smbApplyFileBatchDetail.batchNo},
            #{smbApplyFileBatchDetail.depositSerno},#{smbApplyFileBatchDetail.equityAmount},#{smbApplyFileBatchDetail.customerAmount},#{smbApplyFileBatchDetail.partnerAmount},
            #{smbApplyFileBatchDetail.grantDate},#{smbApplyFileBatchDetail.accountName},#{smbApplyFileBatchDetail.accountNo},#{smbApplyFileBatchDetail.bankAccountNum},
            #{smbApplyFileBatchDetail.bankAccountName},#{smbApplyFileBatchDetail.code},#{smbApplyFileBatchDetail.failDesc},#{smbApplyFileBatchDetail.remark})
        </foreach>
    </insert>

    <delete id="deleteByBatchNo">
        delete
        from smb_apply_file_batch_detail
        where batch_no = #{batchNo}
          and deleted = 0
    </delete>

    <select id="totalByBatchNo" resultType="java.lang.Integer">
        select count(*) from smb_apply_file_batch_detail
        <where>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="resultCode != null and resultCode != ''">
                and code = #{resultCode}
            </if>
            and deleted = 0
        </where>
    </select>

    <select id="pageByBatchNo" resultType="com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail">
        select * from smb_apply_file_batch_detail
        <where>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="resultCode != null and resultCode != ''">
                and code = #{resultCode}
            </if>
            and deleted = 0
        </where>
        limit #{offset},#{batchSize}
    </select>

    <select id="queryRepetitionDepositSerno" resultType="java.lang.String">
        select deposit_serno
        from smb_apply_file_batch_detail
        where batch_no = #{batchNo}
          and deleted = 0
        group by deposit_serno
        having count(*) > 1
    </select>

    <update id="updateFailDescByBatchNoAndDepositSerno">
        update smb_apply_file_batch_detail set fail_desc =
        concat(if(fail_desc is null,'',concat(fail_desc,'、')),#{failDesc}),code = #{code} where batch_no = #{batchNo} and
        deleted = 0
        <if test="depositSernos != null and depositSernos.size() > 0">
            and deposit_serno in
            <foreach collection="depositSernos" item="depositSerno" separator="," open="(" close=")">
                #{depositSerno}
            </foreach>
        </if>
    </update>

    <update id="updateCodeAndFailDescByBatchNoAndDepositSerno">
        update smb_apply_file_batch_detail set fail_desc =
        CASE id
        <foreach collection="details" item="item" index="index">
            when #{item.id} THEN concat(if(fail_desc is null,'',concat(fail_desc,'、')),#{item.failDesc})
        </foreach>
        end,
        code =
        CASE id
        <foreach collection="details" item="item" index="index">
            when #{item.id} THEN #{item.code}
        </foreach>
        end
        where batch_no = #{batchNo} and deleted = 0
        <if test="details != null and details.size() > 0">
            and deposit_serno in
            <foreach collection="details" item="itemDto" separator="," open="(" close=")">
                #{itemDto.depositSerno}
            </foreach>
        </if>
    </update>

    <select id="getSmbOrderResultDetailDTOList" resultType="com.chili.vas.smb.biz.dto.SmbOrderResultDetailDTO">
        select safbd.deposit_serno,
               safbd.equity_amount,
               safbd.customer_amount,
               safbd.partner_amount,
               safbd.grant_date,
               safbd.code,
               safbd.fail_desc,
               so.secret
        from smb_apply_file_batch_detail safbd
                 left join smb_order so on safbd.deposit_serno = so.deposit_serno and so.deleted = 0
        where safbd.deleted = 0
          and safbd.batch_no = #{batchNo}
        ORDER BY safbd.id
        limit #{offset},#{batchSize}
    </select>

    <!-- updateCodeByBatchNo --> 

    <update id="updateCodeByBatchNo">
        update smb_apply_file_batch_detail set code = #{newCode} where batch_no = #{batchNo} and code = #{oldCode} and deleted = 0
    </update>


    <update id="updateCodeMessageByBatchNo">
        UPDATE smb_apply_file_batch_detail safbd
            INNER JOIN smb_order so ON safbd.deposit_serno = so.deposit_serno AND so.deleted = 0
            SET safbd.CODE = #{newCode},safbd.fail_desc = concat(if(safbd.fail_desc is null,'',concat(safbd.fail_desc,'、')), #{message})
        WHERE
            safbd.batch_no = #{batchNo}
          AND safbd.CODE = #{oldCode}
          AND safbd.deleted = 0
    </update>

    <update id="updateCurrentFileCodeMessageByBatchNo">
        UPDATE smb_apply_file_batch_detail safbd
            INNER JOIN (
            SELECT deposit_serno
            FROM smb_apply_file_batch_detail
            WHERE batch_no = #{batchNo} AND deleted = 0
            GROUP BY deposit_serno
            HAVING COUNT(deposit_serno) > 1
            ) safbdct ON safbd.deposit_serno = safbdct.deposit_serno
            SET safbd.code = #{newCode},safbd.fail_desc = #{failDesc}
        WHERE safbd.batch_no = #{batchNo}
          AND safbd.code = #{oldCode}
          AND safbd.deleted = 0
    </update>
</mapper>
