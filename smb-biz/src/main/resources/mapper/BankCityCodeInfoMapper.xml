<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.BankCityCodeInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.BankCityCodeInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="bra_bank_name" property="braBankName" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, deleted, bra_bank_name, city_code, city_name, province_code, province_name
    </sql>

    <select id="findOneByBraBankName" resultType="com.chili.vas.smb.biz.entity.BankCityCodeInfo">
        select * from bank_city_code_info where deleted = 0 and bra_bank_name = #{braBankName}
    </select>

    <select id="findCityCodeByBraBankName" resultType="java.lang.String">
        select city_code from bank_city_code_info where deleted = 0 and bra_bank_name = #{braBankName}
    </select>

</mapper>
