<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.AdminUserMapper">


    <select id="selectByAccount" resultType="com.chili.vas.smb.biz.entity.AdminUser">
        select * from admin_user
        <where>
            deleted = 0
            <if test="account != null and account != ''">
                and user_account = #{account}
            </if>
            <if test="enable != null">
                and enabled = #{enable}
            </if>
        </where>
    </select>
</mapper>
