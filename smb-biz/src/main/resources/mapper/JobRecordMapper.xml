<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.JobRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.JobRecord">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="job_id" property="jobId" />
        <result column="job_code" property="jobCode" />
        <result column="status" property="status" />
        <result column="result" property="result" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, job_id, job_code, status, result, deleted
    </sql>

    <!-- findLastOneByJobId --> 

    <select id="findLastOneByJobId" resultType="com.chili.vas.smb.biz.entity.JobRecord">
        select
        <include refid="Base_Column_List" />
        from job_record
        where job_id = #{jobId}
        and deleted = 0
        order by create_time desc
        limit 1
    </select>
</mapper>
