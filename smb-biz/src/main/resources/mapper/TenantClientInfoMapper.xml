<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chili.vas.smb.biz.mapper.TenantClientInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chili.vas.smb.biz.entity.TenantClientInfo">
        <id column="id" property="id" />
        <result column="client_id" property="clientId" />
        <result column="client_secret" property="clientSecret" />
        <result column="enable" property="enable" />
        <result column="description" property="description" />
        <result column="pre_grant_type" property="preGrantType" />
        <result column="grant_type" property="grantType" />
        <result column="pre_auth_expire_in_seconds" property="preAuthExpireInSeconds" />
        <result column="access_token_expire_in_seconds" property="accessTokenExpireInSeconds" />
        <result column="access_token_transition_in_seconds" property="accessTokenTransitionInSeconds" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_id, client_secret, enable, description, pre_grant_type, grant_type, pre_auth_expire_in_seconds, access_token_expire_in_seconds, access_token_transition_in_seconds, deleted, create_time, update_time, create_by, update_by, tenant_id
    </sql>

    <select id="selectByClintId" resultType="com.chili.vas.smb.biz.entity.TenantClientInfo">
        select * from tenant_client_info
        <where>
            deleted = 0
            <if test="clientId != null and clientId != ''">
                and client_id = #{clientId}
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>
        </where>
    </select>
</mapper>
