<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chili.vas.smb</groupId>
        <artifactId>smb-vas</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>smb-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <smb.api.starter>1.2</smb.api.starter>
        <smb.api>1.3</smb.api>
    </properties>

    <dependencies>
        <!-- 私库 -->
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-http-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-exception-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-jackson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-log-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-opt-log-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-pageable-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-lock-redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-signature-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-file-manager-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-file-biz-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-message-spring-boot-starter</artifactId>
        </dependency>


        <!-- 外部库 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--  苏商银行依赖  -->
        <dependency>
            <groupId>com.snb.fsos</groupId>
            <artifactId>snb-fsos-api-starter</artifactId>
            <version>${smb.api.starter}</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/snb-fsos-api-starter-1.2.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.snb.fsos</groupId>
            <artifactId>snb-fsos-api</artifactId>
            <version>${smb.api}</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/snb-fsos-api-1.3.jar</systemPath>
        </dependency>

        <!-- 使用最新稳定版本 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
        </dependency>
        <!-- 使用最新稳定版本 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.16</version>
        </dependency>
        <!-- Apache HttpComponents Client -->
        <!-- 使用最新稳定版本 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <!-- 使用最新版本 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>
        <!-- 使用最新的稳定版本 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.0</version>
        </dependency>
    </dependencies>

</project>
