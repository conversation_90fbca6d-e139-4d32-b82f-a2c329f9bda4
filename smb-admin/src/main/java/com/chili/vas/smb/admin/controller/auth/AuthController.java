package com.chili.vas.smb.admin.controller.auth;

import com.chili.vas.smb.admin.common.annotation.namespace.AdminAuthMapping;
import com.chili.vas.smb.admin.ext.captcha.Captcha;
import com.chili.vas.smb.admin.ext.captcha.CaptchaGenerator;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.PreAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultPreAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.AuthenticationInfoDTO;
import com.wftk.auth.spring.boot.autoconfigure.ext.dto.PreAuthenticationDTO;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * AuthController 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:20
 */
@Slf4j
@AdminAuthMapping
@RestController
@Tag(name = "Auth相关api")
public class AuthController {
    @Autowired
    private PreAuthenticationManager preAuthenticationManager;

    @Autowired
    private AccessTokenManager accessTokenManager;

    @Autowired
    private CaptchaGenerator captchaGenerator;

    @Operation(summary = "预授权")
    @PostMapping("/token/pre")
    public ApiResult<String> preAuth(@RequestBody @Valid PreAuthenticationDTO preAuthenticationInfoDTO){
        Captcha captcha = captchaGenerator.generateCaptcha();
        if (Objects.isNull(captcha)){
            throw new BusinessException("获取验证码失败，请稍后再试");
        }
        DefaultPreAuthenticationInfo preAuthenticationInfo = preAuthenticationInfoDTO.toEntity();
        preAuthenticationInfo.setPreAuthCode(captcha.getCode());
        preAuthenticationManager.preAuthenticate(preAuthenticationInfo);
        return ApiResult.ok(captcha.getCaptchaBase64());
    }


    @Operation(summary = "登录认证")
    @PostMapping("/token")
    public ApiResult<AccessTokenInfo> auth(@RequestBody @Valid AuthenticationInfoDTO authenticationInfoDTO) {
        authenticationInfoDTO.setPreAuthCode(authenticationInfoDTO.getPreAuthCode().toLowerCase());
        DefaultAuthenticationInfo authenticationInfo = authenticationInfoDTO.toEntity();
        Authentication authenticate = preAuthenticationManager.authenticate(authenticationInfo);
        AccessTokenInfo tokenInfo = accessTokenManager.grant(authenticate);
        return ApiResult.ok(tokenInfo);
    }
}
