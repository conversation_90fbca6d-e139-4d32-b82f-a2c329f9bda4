package com.chili.vas.smb.admin.vo.output;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.serializer.rmb.LongRMBFenToYuanSerializer;
import com.wftk.jackson.serializer.rmb.RMBFenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * CosignOrderOutput 类
 *
 * <AUTHOR>
 * @since 2025/6/20 10:27
 */
@Data
public class ConsignOrderOutput {

    @Schema(description = "ID")
    private Long id;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 0：待处理；1：处理中; 2：成功；11：失败
     */
    @Schema(description = "订单状态 0：待处理；1：处理中; 2：成功；11：失败")
    private Integer status;

    /**
     * 代付人姓名
     */
    @Schema(description = "代付人姓名")
    private String name;

    /**
     * 代付人银行卡号
     */
    @Schema(description = "代付人银行卡号")
    private String bankAccountNo;

    /**
     * 寄售金额（分）寄售平台的为元
     */
    @Schema(description = "寄售金额（分）寄售平台的为元")
    @JsonSerialize(using = RMBFenToYuanSerializer.class)
    private Integer amount;

    /**
     * 苏商订单号
     */
    @Schema(description = "苏商订单号")
    private String smbOrderNo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 代付渠道编号。民生、连连、拉卡拉
     */
    @Schema(description = "代付渠道编号。民生、连连、拉卡拉")
    private String payChannel;

    /**
     * 代付渠道的返回结果码，不同代付渠道，结果码不一样
     */
    @Schema(description = "代付渠道的返回结果码，不同代付渠道，结果码不一样")
    private String retCode;

    /**
     * 最新发起寄售时间
     */
    @Schema(description = "最新发起寄售时间")
    private LocalDateTime transactionTime;

    /**
     * 寄售完成时间
     */
    @Schema(description = "寄售完成时间")
    private LocalDateTime transactionFinishTime;

    /**
     * 是否可以手动重试（1：可以；0不可以）
     */
    @Schema(description = "是否可以手动重试（1：可以；0不可以）")
    private Integer manualRetry;

    /**
     * 外部交易号
     */
    @Schema(description = "外部交易号")
    private String extTradeNo;

    /**
     * 开户行名
     */
    @Schema(description = "开户行名")
    private String bankAccountName;

    /**
     * 1 - 对公。0 - 对私。
     */
    @Schema(description = "1 - 对公。0 - 对私。")
    private String flagCard;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
