package com.chili.vas.smb.admin.controller.smb;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.chili.vas.smb.admin.common.annotation.namespace.SmbOrderMapping;
import com.chili.vas.smb.admin.common.optlog.OptLogConstant;
import com.chili.vas.smb.admin.converter.ConsignOrderAdminConverter;
import com.chili.vas.smb.admin.converter.SmbOrderAdminConverter;
import com.chili.vas.smb.admin.vo.input.ManualWriteOffInput;
import com.chili.vas.smb.admin.vo.input.OrderRetryInput;
import com.chili.vas.smb.admin.vo.input.PaymentRecordInput;
import com.chili.vas.smb.admin.vo.input.SmbOrderQueryInput;
import com.chili.vas.smb.admin.vo.output.ConsignOrderDetailsOutput;
import com.chili.vas.smb.admin.vo.output.SmbOrderOutput;
import com.chili.vas.smb.admin.vo.output.WriteOffStatusNoticeOutput;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.WriteOffStatusEnum;
import com.chili.vas.smb.biz.common.lock.LockManager;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.service.ConsignOrderService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.ext.password.BcryptPasswordEncoder;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * SmbOrder 类
 *
 * <AUTHOR>
 * @since 2025/6/19 18:33
 */
@Tag(name = "权益订单API")
@RestController
@SmbOrderMapping
@Slf4j
@RequiredArgsConstructor
public class SmbOrderController {

    private final SmbOrderService smbOrderService;
    private final ConsignOrderService consignOrderService;
    private final SmbOrderAdminConverter smbOrderAdminServiceConverter;
    private final ConsignOrderAdminConverter consignOrderAdminServiceConverter;
    private final LockManager lockManager;


    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.QUERY, description = "苏商订单分页列表查询")
    @Operation(summary = "分页列表")
    @GetMapping
    public ApiResult<Page<SmbOrderOutput>> getSmbOrderPage(SmbOrderQueryInput input) {
        SmbOrder smbOrder = smbOrderAdminServiceConverter.inputToEntity(input);
        Page<SmbOrder> smbOrderList = smbOrderService.getSmbOrderPage(smbOrder,input.getPageNum(),input.getPageSize(),
                input.getStartTime(),input.getEndTime());
        return ApiResult.ok(smbOrderAdminServiceConverter.toSmbOrderOutput(smbOrderList));
    }

    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.QUERY, description = "苏商订单分页列表导出")
    @Operation(summary = "导出")
    @GetMapping("/export")
    public void export(SmbOrderQueryInput input, HttpServletResponse response) {
        SmbOrder smbOrder = smbOrderAdminServiceConverter.inputToEntity(input);
        smbOrderService.export(smbOrder,input.getStartTime(),input.getEndTime(),response);
        //return ApiResult.ok();
    }


    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.QUERY, description = "苏商订单支付记录查询")
    @Operation(summary = "支付记录")
    @GetMapping("/paymentRecord")
    public ApiResult<Page<ConsignOrderDetailsOutput>> paymentRecord(PaymentRecordInput input) {
        Page<ConsignOrder> page =  consignOrderService.paymentRecord(input.getSmbOrderNo(),input.getPageNum(),input.getPageSize());
        return ApiResult.ok(consignOrderAdminServiceConverter.entityPageToDetailsOutputPage(page));
    }

    @Operation(summary = "订单重试")
    @PostMapping("/orderRetry")
    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.MODIFY, description = "苏商订单重试")
    public ApiResult<Void> orderRetry(@RequestBody @Valid OrderRetryInput orderRetryInput) {
        DLock dLock = lockManager.getOrderRetryLock(orderRetryInput.getOrderNo());
        try {
            if (dLock.tryLock()){
                smbOrderService.orderRetry(orderRetryInput.getOrderNo());
            }else {
                log.warn("order retry lock fail. order no is {}", orderRetryInput.getOrderNo());
                throw new BusinessException("订单处理中，请稍后再试！");
            }
        }finally {
            dLock.unLock();
        }
        return ApiResult.ok();
    }

    @Operation(summary = "人工核销")
    @PostMapping("/manualWriteOff")
    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.MODIFY, description = "苏商订单人工核销")
    public ApiResult<Void> manualWriteOff(@RequestBody @Valid ManualWriteOffInput input) {
        DLock dLock = lockManager.getManualWriteOffLock(input.getOrderNo());
        try {
            if (dLock.tryLock()){
                log.info("SmbOrderController manualWriteOff request param:{}", JSONObject.getInstance().toJSONString(input));
                smbOrderService.manualWriteOff(input.getOrderNo(),input.getSmbOrderStatus(),input.getManualRemark());
            }else {
                log.warn("manual write off lock fail. order no is {}", input.getOrderNo());
                throw new BusinessException("订单处理中，请稍后再试！");
            }
        }finally {
            dLock.unLock();
        }


        return ApiResult.ok();
    }

    @Operation(summary = "人工核销通知枚举列表")
    @PostMapping("/getSmbOrderStatusList")
    @OptLog(module = OptLogConstant.MODULE.SMB_ORDER, optType = OptType.QUERY, description = "人工核销通知枚举列表")
    public ApiResult<List<WriteOffStatusNoticeOutput>> getSmbOrderStatusEnumList() {
        List<WriteOffStatusNoticeOutput> list = new ArrayList<>();
        WriteOffStatusNoticeOutput output = null;
        List<WriteOffStatusEnum> writeOffStatusEnumList = WriteOffStatusEnum.getSmbOrderStatusList();
        for (WriteOffStatusEnum writeOffStatusEnum : writeOffStatusEnumList) {
            output = new WriteOffStatusNoticeOutput();
            output.setValue(writeOffStatusEnum.getValue());
            output.setLabel(writeOffStatusEnum.getDesc());
            list.add(output);
        }
        return ApiResult.ok(list);
    }



    public static void main(String[] args) {
       PasswordEncoder passwordEncoder = new BcryptPasswordEncoder();
       String password = passwordEncoder.encode("123456");
        System.out.println( password);
    }
}
