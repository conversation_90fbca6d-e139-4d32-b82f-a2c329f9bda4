package com.chili.vas.smb.admin.controller.consign;

import com.chili.vas.smb.admin.common.annotation.namespace.CosignOrderMapping;
import com.chili.vas.smb.admin.common.optlog.OptLogConstant;
import com.chili.vas.smb.admin.converter.ConsignOrderAdminConverter;
import com.chili.vas.smb.admin.vo.input.CosignOrderQueryInput;
import com.chili.vas.smb.admin.vo.output.ConsignOrderOutput;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.service.ConsignOrderService;
import com.wftk.common.core.result.ApiResult;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * CosignOrder 类
 *
 * <AUTHOR>
 * @since 2025/6/19 18:58
 */
@Tag(name = "寄售订单API")
@RestController
@CosignOrderMapping
@Slf4j
@RequiredArgsConstructor
public class CosignOrderController {

    private final ConsignOrderService consignOrderService;

    private final ConsignOrderAdminConverter consignOrderAdminConverter;


    @OptLog(module = OptLogConstant.MODULE.CONSIGN_ORDER, optType = OptType.QUERY, description = "寄售订单分页列表查询")
    @Operation(summary = "分页列表")
    @GetMapping
    public ApiResult<Page<ConsignOrderOutput>> getCosignOrderPage(CosignOrderQueryInput input) {
        ConsignOrder consignOrder = consignOrderAdminConverter.inputToEntity(input);
        Page<ConsignOrder> page = consignOrderService.getCosignOrderPage(consignOrder, input.getPageNum(),input.getPageSize(),input.getStartTime(),input.getEndTime());
        Page<ConsignOrderOutput> pageOutput = consignOrderAdminConverter.entityPageToOutputPage(page);
        return ApiResult.ok(pageOutput);
    }

    @OptLog(module = OptLogConstant.MODULE.CONSIGN_ORDER, optType = OptType.QUERY, description = "寄售订单分页列表导出")
    @Operation(summary = "分页列表导出")
    @GetMapping("export")
    public void export(CosignOrderQueryInput input, HttpServletResponse response) {
        ConsignOrder consignOrder = consignOrderAdminConverter.inputToEntity(input);
        consignOrderService.export(consignOrder,input.getStartTime(),input.getEndTime(),response);
    }
}
