package com.chili.vas.smb.admin.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.exception.auth.InvalidPreAuthAccountException;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * InvalidPreAuthAccountExceptionTranslator 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:46
 */
public class InvalidPreAuthAccountExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), "账号错误", HttpStatusCode.BAD_REQUEST);

    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof InvalidPreAuthAccountException;
    }
}
