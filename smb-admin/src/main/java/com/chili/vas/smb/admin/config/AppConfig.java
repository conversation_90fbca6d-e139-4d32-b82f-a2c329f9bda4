package com.chili.vas.smb.admin.config;

import com.chili.vas.smb.admin.ext.auth.SystemClientLoader;
import com.chili.vas.smb.admin.ext.captcha.CaptchaGenerator;
import com.chili.vas.smb.biz.service.TenantClientInfoService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * AppConfig 类
 *
 * <AUTHOR>
 * @since 2025/6/19 17:16
 */
@Configuration
@ComponentScan(basePackages = "com.chili.vas.smb")
@MapperScan("com.chili.vas.smb.**.mapper")
public class AppConfig {
    @Bean
    CaptchaGenerator captchaGenerator() {
        return new CaptchaGenerator();
    }



}
