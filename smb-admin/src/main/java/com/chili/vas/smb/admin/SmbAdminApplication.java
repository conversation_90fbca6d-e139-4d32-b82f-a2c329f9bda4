package com.chili.vas.smb.admin;

import com.wftk.file.manager.spring.boot.autoconfigure.oss.EnableOSSClient;
import com.wftk.jackson.spring.boot.autoconfigure.datetime.EnableDateTimeFormatter;
import com.wftk.opt.log.spring.boot.autoconfigure.EnableOptLog;
import com.wftk.signature.spring.boot.autoconfigure.EnableSignatureFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableOptLog
@EnableSignatureFilter
@EnableDateTimeFormatter
@SpringBootApplication(scanBasePackages = { "com.chili.vas.smb"})
@EnableOSSClient
@EnableScheduling
@EnableAsync
public class SmbAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmbAdminApplication.class, args);
    }

}
