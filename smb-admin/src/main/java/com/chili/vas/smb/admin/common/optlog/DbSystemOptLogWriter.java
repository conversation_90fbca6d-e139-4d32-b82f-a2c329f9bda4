package com.chili.vas.smb.admin.common.optlog;

import cn.hutool.core.collection.CollectionUtil;
import com.chili.vas.smb.admin.converter.SystemOptLogConverter;
import com.chili.vas.smb.biz.entity.SystemOptLog;
import com.chili.vas.smb.biz.service.SystemOptLogService;
import com.wftk.opt.log.spring.boot.autoconfigure.log.DefaultSysOptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.OptLogWriter;

import java.util.Map;

public class DbSystemOptLogWriter implements OptLogWriter<DefaultSysOptLog> {

    private final SystemOptLogService systemOptLogService;

    private final SystemOptLogConverter systemOptLogConverter;

    public DbSystemOptLogWriter(SystemOptLogService systemOptLogService, SystemOptLogConverter systemOptLogConverter){
        this.systemOptLogConverter = systemOptLogConverter;
        this.systemOptLogService = systemOptLogService;
    }

    @Override
    public void write(DefaultSysOptLog log) {
        Map<String, String> headers = log.getHeaders();
        SystemOptLog systemOptLog = systemOptLogConverter.defaultLogToEntity(log);
        if (CollectionUtil.isNotEmpty(headers) && headers.containsKey("user-agent")) {
            systemOptLog.setUserAgent(headers.get("user-agent"));
        }
        systemOptLogService.save(systemOptLog);
    }
}
