package com.chili.vas.smb.admin.vo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ConsignOrderDetailsOutput 类
 *
 * <AUTHOR>
 * @since 2025/6/20 11:03
 */
@Data
public class ConsignOrderDetailsOutput {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "支付流水号")
    private String extTradeNo;


    @Schema(description = "寄售订单号")
    private String orderNo;

    @Schema(description = "银行账号")
    private String bankAccountNo;

    @Schema(description = "操作时间")
    private LocalDateTime createTime;

    @Schema(description = "订单状态 0：待处理；1：处理中; 2：成功；11：失败")
    private Integer status;

    @Schema(description = "备注")
    private String remark;
}
