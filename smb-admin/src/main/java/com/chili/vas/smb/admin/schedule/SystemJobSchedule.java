package com.chili.vas.smb.admin.schedule;

import com.chili.vas.smb.biz.common.constant.JobConstant;
import com.chili.vas.smb.biz.common.enums.JobStatusEnum;
import com.chili.vas.smb.biz.common.lock.LockManager;
import com.chili.vas.smb.biz.job.JobExecutor;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SystemJobSchedule {

    @Autowired
    private JobExecutor jobExecutor;

    @Autowired
    private LockManager lockManager;

    /**
     * 执行待执行任务
     */
    @Scheduled(fixedDelay = 30 * 1000)
    public void executeSmbPendingJobs() {
        DLock lock = lockManager.getScheduleSmbPendingJobExecutionLock();
        try {
            if (lock.tryLock()) {
                log.info("execute smb pending jobs");
                jobExecutor.execute(JobConstant.SceneCode.SMB_ORDER_WRITE_OFF, JobStatusEnum.PENDING);
            }else{
                log.warn("execute smb pending jobs lock get fail, ignore...");
            }
        } finally {
            lock.unLock();
        }
    }
}
