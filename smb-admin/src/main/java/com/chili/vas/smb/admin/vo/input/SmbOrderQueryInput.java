package com.chili.vas.smb.admin.vo.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SmbOrderQueryInput 类
 *
 * <AUTHOR>
 * @since 2025/6/19 19:08
 */
@Data
public class SmbOrderQueryInput extends BaseQueryInput implements Serializable {
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "苏商流水号")
    private String depositSerno;

    /**
     * 收款名称
     */
    @Schema(description = "收款名称")
    private String accountName;


    @Schema(description = "收款账户")
    private String accountNo;

    /**
     * 状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 12.已取消
     */
    @Schema(description = "状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 ")
    private Integer status;

    /**
     * 核销状态
     */
    @Schema(description = "核销状态")
    private Integer writeOffStatus;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
