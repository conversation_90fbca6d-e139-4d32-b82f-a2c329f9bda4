package com.chili.vas.smb.admin.ext.auth;

import com.chili.vas.smb.biz.entity.AdminUser;
import com.chili.vas.smb.biz.service.AdminUserService;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * AdminUserLoader 类
 *
 * <AUTHOR>
 * @since 2025/6/19 10:40
 */
@Slf4j
public class AdminUserLoader implements UserLoader<AdminUser> {

    private final AdminUserService adminUserService;

    public AdminUserLoader(AdminUserService adminUserService) {
        this.adminUserService = adminUserService;
    }


    @Override
    public AuthUser<AdminUser> load(String account) {
        AdminUser adminUser = adminUserService.findOneByAccount(account, true);
        if (adminUser == null){
            return null;
        }
        return new AdminAuthUser(adminUser);
    }
}
