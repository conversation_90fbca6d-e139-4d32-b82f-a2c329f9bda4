package com.chili.vas.smb.admin.schedule.smb;

import com.chili.vas.smb.admin.common.constants.MessageBusinessJobConstant;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.job.SystemJob;
import com.chili.vas.smb.biz.job.context.SystemJobContext;
import com.chili.vas.smb.biz.service.FeishuMessageService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


public abstract class SmbNotifyJob implements SystemJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    protected final SmbOrderService smbOrderService;


    @Autowired
    private FeishuMessageService feishuMessageService;

    public SmbNotifyJob(SmbOrderService smbOrderService) {
        this.smbOrderService = smbOrderService;
    }

    public void execute(SystemJobContext context) {
        logger.info("SmbNotifyJob job start...");
        SmbOrder smbOrder = null;
        try {
            smbOrder = (SmbOrder) context.getParamMap().get("smbOrderInfo");
            if (smbOrder == null) {
                Long bizId = (Long) context.getParamMap().get("bizId");
                if (bizId == null) {
                    throw new IllegalArgumentException("invalid bizId: " + bizId);
                }
                smbOrder = smbOrderService.getById(bizId);
                if (smbOrder == null) {
                    throw new IllegalArgumentException("invalid bizId: " + bizId);
                }
            }
            doExecute(smbOrder);
        } catch (Exception e) {
            logger.error("execute job error", e);
            sendFeishuMessage(smbOrder,e);
            context.addException(getJobCode(), e);

        }
    }

    protected abstract void doExecute(SmbOrder smbOrder);


    /**
     *  失败发送飞书告警
     * @param smbOrder
     * @param e
     */
    private void sendFeishuMessage(SmbOrder smbOrder, Exception e) {
        try {
            logger.info("execute job failed feishu start....");
            Map<String,Object> params = new HashMap<>();
            if (Objects.nonNull(smbOrder)){
                params.put("orderNo", smbOrder.getOrderNo());
            }
            params.put("jobCode",  getJobCode());
            params.put("status","失败");
            params.put("titleColor","red");
            params.put("remark",e.getMessage());
            feishuMessageService.send(MessageBusinessJobConstant.MESSAGE_BUSINESS_JOB,params);
            logger.info("execute job failed feishu end....");
        }catch (Exception ex){
            logger.error("sendFeishuMessage error", ex);
        }
    }
}
