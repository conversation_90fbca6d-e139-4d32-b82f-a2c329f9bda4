package com.chili.vas.smb.admin.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.MethodOptLogBuilder;

/**
 * AdminUserInfoHolder 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:46
 */
public class AdminUserInfoHolder implements MethodOptLogBuilder.UserInfoHolder {

    @Override
    public String getUserId() {
        Authentication authentication = AuthenticationHolder.getAuthentication();
        if (authentication != null && authentication.getAuthUser() != null) {
            return String.valueOf(authentication.getAuthUser().getId());

        }
        return AnyoneUserEnum.ID.getValue();
    }

    @Override
    public String getUserName() {
        Authentication authentication = AuthenticationHolder.getAuthentication();
        if (authentication != null) {
            AdminAuthUser adminAuthUser = (AdminAuthUser) authentication.getAuthUser();
            if (adminAuthUser != null) {
                return adminAuthUser.getUser() == null ? AnyoneUserEnum.NAME.getValue() : adminAuthUser.getUser().getName();
            }
        }
        return AnyoneUserEnum.NAME.getValue();
    }

    @Override
    public Integer getUserType() {
        return 3;
    }
}
