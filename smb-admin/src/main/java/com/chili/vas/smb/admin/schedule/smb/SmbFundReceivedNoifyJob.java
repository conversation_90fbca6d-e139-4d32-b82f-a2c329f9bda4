package com.chili.vas.smb.admin.schedule.smb;

import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffRecordStatusEnum;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import com.chili.vas.smb.biz.service.SmbBusinessService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.biz.service.SmbOrderWriteOffRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * 批次明细写入
 * @createDate 2025/5/27 10:07
 */
@Slf4j
@Component
public class SmbFundReceivedNoifyJob extends SmbNotifyJob {

    @Autowired
    SmbOrderService smbOrderService;

    @Autowired
    SmbOrderWriteOffRecordService smbOrderWriteOffRecordService;

    @Autowired
    SmbBusinessService smbBusinessService;

    public SmbFundReceivedNoifyJob(SmbOrderService smbOrderService) {
        super(smbOrderService);
    }

    @Override
    protected void doExecute(SmbOrder smbOrder) {
        SmbOrderWriteOffRecord writeOffRecord = smbOrderWriteOffRecordService.findOneBySmbOrderNo(smbOrder.getOrderNo());

        if(writeOffRecord == null){
            log.error("核销记录查询失败, smbOrderNo is {}", smbOrder.getOrderNo());
            throw new SmbJobExecuteException("核销记录查询失败");
        }

        if(Objects.equals(writeOffRecord.getStatus(), SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue())){
            throw new SmbJobExecuteException("正在核销中，请勿重新核销");
        }
        if(Objects.equals(writeOffRecord.getStatus(), SmbOrderWriteOffRecordStatusEnum.SUCCESS.getValue())){
            throw new SmbJobExecuteException("已成功核销，请勿重新核销");
        }

        SmbOrderWriteOffRecord updateWriteOffRecord = new SmbOrderWriteOffRecord();
        updateWriteOffRecord.setId(writeOffRecord.getId());
        updateWriteOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.PROCESS.getValue());
        smbOrderWriteOffRecordService.updateById(updateWriteOffRecord);

        // 苏商核销
        smbBusinessService.notifyToSmb(smbOrder,writeOffRecord);

    }



    @Override
    public String getJobCode() {
        return "smb_fund_received_noify";
    }

}
