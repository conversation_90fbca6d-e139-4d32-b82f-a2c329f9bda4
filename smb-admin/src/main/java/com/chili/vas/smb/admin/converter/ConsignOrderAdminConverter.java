package com.chili.vas.smb.admin.converter;

import com.chili.vas.smb.admin.vo.input.CosignOrderQueryInput;
import com.chili.vas.smb.admin.vo.output.ConsignOrderDetailsOutput;
import com.chili.vas.smb.admin.vo.output.ConsignOrderOutput;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import org.mapstruct.Mapper;

/**
 * ConsignOrderAdminConverter 接口
 *
 * <AUTHOR>
 * @since 2025/6/20 10:42
 */
@Mapper(componentModel = "spring")
public interface ConsignOrderAdminConverter {

    ConsignOrder inputToEntity(CosignOrderQueryInput consignOrderQueryInput);

    Page<ConsignOrderOutput> entityPageToOutputPage(Page<ConsignOrder> consignOrderPage);


    Page<ConsignOrderDetailsOutput> entityPageToDetailsOutputPage(Page<ConsignOrder> consignOrderPage);
}
