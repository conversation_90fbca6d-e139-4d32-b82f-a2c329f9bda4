package com.chili.vas.smb.admin.vo.output;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.serializer.rmb.RMBFenToYuanSerializer;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation.BankCard;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation.Name;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SmbOrderOutput 类
 *
 * <AUTHOR>
 * @since 2025/6/19 19:21
 */
@Data
public class SmbOrderOutput implements Serializable {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "苏商流水号")
    private String depositSerno;

    @Schema(description = "收款名称")
    private String accountName;


    @Schema(description = "收款账户")
    private String accountNo;

    @Schema(description = "开户行号")
    private String bankAccountNum;

    @Schema(description = "开户行名")
    private String bankAccountName;

    @Schema(description = "卡密")
    private String secret;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "激活时间")
    private LocalDateTime activateTime;

    @Schema(description = "权益金额(元)")
    @JsonSerialize(using = RMBFenToYuanSerializer.class)
    private  Integer equityAmount;

    @Schema(description = "客户收益(元)")
    @JsonSerialize(using = RMBFenToYuanSerializer.class)
    private Integer customerAmount;

    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "核销时间")
    private LocalDateTime writeOffTime;

    @Schema(description = "状态 0.待激活 1.已激活 2.寄售中 3.寄售成功 11. 寄售失败 12.已取消")
    private Integer status;


    @Schema(description = "核销状态 0.未核销 1.已核销  2.核销中")
    private Integer writeOffStatus;


    @Schema(description = "是否能核销 ,true 能 ,false 不能")
    private boolean isWriteOff;

    @Schema(description = "是否能重试 ,true 能 ,false 不能")
    private boolean orderRetry;

}
