package com.chili.vas.smb.admin.ext.sign;

import com.chili.vas.smb.biz.entity.TenantClientInfo;
import com.chili.vas.smb.biz.service.TenantClientInfoService;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.loader.ClientCredentialLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * TenantClientCredentialLoader 类
 *
 * <AUTHOR>
 * @since 2025/6/24 10:30
 */
@Slf4j
public class TenantClientCredentialLoader implements ClientCredentialLoader {
    private final TenantClientInfoService tenantClientInfoService;

    public TenantClientCredentialLoader(TenantClientInfoService tenantClientInfoService) {
        this.tenantClientInfoService = tenantClientInfoService;
    }

    @Override
    public ClientInfo get(String clientId) {
        TenantClientInfo tenantClientInfo = tenantClientInfoService.findOneByClientId(clientId, true);
        if (tenantClientInfo == null) {
            log.warn("invalid clientId: {}", clientId);
            return null;
        }
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientId(tenantClientInfo.getClientId());
        clientInfo.setClientSecret(tenantClientInfo.getClientSecret());
        return clientInfo;
    }
}
