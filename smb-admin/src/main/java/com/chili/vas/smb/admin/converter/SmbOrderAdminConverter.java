package com.chili.vas.smb.admin.converter;

import com.chili.vas.smb.admin.vo.input.SmbOrderQueryInput;
import com.chili.vas.smb.admin.vo.output.SmbOrderOutput;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import org.mapstruct.Mapper;

/**
 * SmbOrderAdminServiceConverter 接口
 *
 * <AUTHOR>
 * @since 2025/6/19 19:20
 */
@Mapper(componentModel = "spring")
public interface SmbOrderAdminConverter {

    Page<SmbOrderOutput> toSmbOrderOutput(Page<SmbOrder> smbOrderPage);

    SmbOrder inputToEntity(SmbOrderQueryInput smbOrderQueryInput);
}
