package com.chili.vas.smb.admin.ext.captcha;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.core.codec.Base64;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Random;

/**
 * Captcha 类
 *
 * <AUTHOR>
 * @since 2025/6/19 17:26
 */
@Slf4j
public class CaptchaGenerator {

    public Captcha generateCaptcha()  {
        // 创建线段干扰验证码
        CircleCaptcha  captcha = CaptchaUtil.createCircleCaptcha(260, 100, 4, 20);
        captcha.createCode();
        String code = captcha.getCode();
        // 大写字母转为小写字母
        code = code.toLowerCase();
        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            captcha.write(outputStream);
            byte[] bytes = outputStream.toByteArray();
            String base64 = Base64.encode(bytes);
            String captchaBase64 = "data:image/png;base64," + base64;
            return new Captcha(code, captchaBase64);
        } catch (IOException e) {
            log.error("CaptchaGenerator generate captcha error ", e);
            throw new BusinessException("CaptchaGenerator generate captcha error",e);
        }
    }
}
