package com.chili.vas.smb.admin.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.validator.PreAuthAccountValidator;

/**
 * AdminPreAuthAccountValidator 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:43
 */
public class AdminPreAuthAccountValidator implements PreAuthAccountValidator {

    private final AdminUserLoader adminUserLoader;

    public AdminPreAuthAccountValidator(AdminUserLoader adminUserLoader) {
        this.adminUserLoader = adminUserLoader;
    }

    @Override
    public boolean validate(String account, String preGrantType) {
        if ("password".equals(preGrantType)){
            return true;
        }
        return adminUserLoader.load( account) != null;
    }
}
