package com.chili.vas.smb.admin.vo.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * CosignOrderQueryInput 类
 *
 * <AUTHOR>
 * @since 2025/6/20 10:01
 */
@Data
public class CosignOrderQueryInput extends BaseQueryInput{

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "苏商订单号")
    private String smbOrderNo;

    @Schema(description = "收款名称")
    private String name;


    @Schema(description = "收款账户")
    private String bankAccountNo;


    /**
     * 0：待处理；1：处理中; 2：成功；11：失败
     */
    @Schema(description = "状态 0.待处理 1.处理中 2.成功 11.失败")
    private Integer status;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
