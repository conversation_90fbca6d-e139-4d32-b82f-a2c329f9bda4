package com.chili.vas.smb.admin.config;


import com.chili.vas.smb.admin.ext.auth.*;
import com.chili.vas.smb.admin.ext.sign.TenantClientCredentialLoader;
import com.chili.vas.smb.biz.service.AdminUserService;
import com.chili.vas.smb.biz.service.TenantClientInfoService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/12/4 16:32
 */
@Configuration
public class AuthConfig {

    @Bean
    SystemClientLoader systemClientLoader(TenantClientInfoService tenantClientInfoService) {
        return new SystemClientLoader(tenantClientInfoService);
    }

    @Bean
    AdminUserLoader adminUserLoader(AdminUserService adminUserService) {
        return new AdminUserLoader(adminUserService);
    }


    @Bean
    AdminPreAuthAccountValidator adminPreAuthAccountValidator(AdminUserLoader adminUserLoader) {
        return new AdminPreAuthAccountValidator(adminUserLoader);
    }

    @Bean
    InvalidPreAuthAccountExceptionTranslator invalidPreAuthAccountExceptionTranslator() {
        return new InvalidPreAuthAccountExceptionTranslator();
    }


    @Bean
    TenantClientCredentialLoader tenantClientCredentialLoader(TenantClientInfoService tenantClientInfoService){
        return new TenantClientCredentialLoader(tenantClientInfoService);
    }
}
