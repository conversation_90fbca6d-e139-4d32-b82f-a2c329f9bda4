package com.chili.vas.smb.admin.config;

import com.chili.vas.smb.admin.common.optlog.DbSystemOptLogWriter;
import com.chili.vas.smb.admin.converter.SystemOptLogConverter;
import com.chili.vas.smb.admin.ext.auth.AdminUserInfoHolder;
import com.chili.vas.smb.biz.service.SystemOptLogService;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.MethodOptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.log.DefaultSysOptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.OptLogWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OptLogConfig {

    @Bean
    OptLogWriter<DefaultSysOptLog> sysOptLogWriter(SystemOptLogService sysOptLogService, SystemOptLogConverter sysOptLogConverter) {
        return new DbSystemOptLogWriter(sysOptLogService, sysOptLogConverter);
    }

    @Bean
    MethodOptLogBuilder.UserInfoHolder userInfoHolder() {
        return new AdminUserInfoHolder();
    }

}
