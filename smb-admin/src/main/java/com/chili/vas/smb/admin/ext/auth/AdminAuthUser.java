package com.chili.vas.smb.admin.ext.auth;

import com.chili.vas.smb.biz.entity.AdminUser;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

/**
 * AdminAuthUser 类
 *
 * <AUTHOR>
 * @since 2025/6/19 15:37
 */
public class AdminAuthUser extends AuthUser<AdminUser> {
    @JsonCreator
    public AdminAuthUser(@JsonProperty("user") AdminUser user) {
        super(user);
    }

    @Override
    public String getTenantId() {
        return null;
    }

    @Override
    public Long getId() {
        return getUser().getId();
    }

    @Override
    public String getAccount() {
        return getUser().getUserAccount();
    }

    @Override
    public String getPassword() {
        return getUser().getPwd();
    }

    @Override
    public boolean isDisabled() {
        return getUser().getEnabled() == null || !getUser().getEnabled();
    }
}
