package com.chili.vas.smb.admin.vo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * ManualWriteOffInput 类
 *
 * <AUTHOR>
 * @since 2025/6/20 13:47
 */
@Data
public class ManualWriteOffInput {
    @Schema(description = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "核销通知结果 成功,失败")
    @NotNull(message = "核销通知结果不能为空")
    private Integer smbOrderStatus;

    @Schema(description = "人工核销备注")
    private String manualRemark;
}
