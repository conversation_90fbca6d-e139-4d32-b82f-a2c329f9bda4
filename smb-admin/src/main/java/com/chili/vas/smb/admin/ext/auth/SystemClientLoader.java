package com.chili.vas.smb.admin.ext.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.TenantClientInfo;
import com.chili.vas.smb.biz.service.TenantClientInfoService;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.client.DefaultClientConfig;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.client.DefaultClientInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * AdminUserLoader 类
 *
 * <AUTHOR>
 * @since 2025/6/19 10:37
 */
@Slf4j
public class SystemClientLoader implements ClientLoader {

    private final TenantClientInfoService tenantClientInfoService;

    public SystemClientLoader(TenantClientInfoService tenantClientInfoService) {
        this.tenantClientInfoService = tenantClientInfoService;
    }


    @Override
    public ClientInfo load(String clientId) {
        TenantClientInfo tenantClientInfo = tenantClientInfoService.findOneByClientId(clientId, true);
        if (tenantClientInfo == null) {
            log.warn("invalid clientId: {}", clientId);
            return null;
        }
        ClientInfo.ClientConfig clientConfig = buildConfig(tenantClientInfo);
        return new DefaultClientInfo(null, tenantClientInfo.getClientId(), tenantClientInfo.getClientSecret(), clientConfig);
    }




    /**
     * 构建客户端配置
     * @param tenantClientInfo
     * @return
     */
    private ClientInfo.ClientConfig buildConfig(TenantClientInfo tenantClientInfo) {
        DefaultClientConfig clientConfig = new DefaultClientConfig();
        if (StrUtil.isNotBlank(tenantClientInfo.getGrantType())) {
            Set<String> grantType = CollUtil.set(false, tenantClientInfo.getGrantType().split(","));
            clientConfig.setGrantType(grantType);
        }
        if (StrUtil.isNotBlank(tenantClientInfo.getPreGrantType())) {
            Set<String> preGrantType =CollUtil.set(false, tenantClientInfo.getPreGrantType().split(","));
            clientConfig.setPreGrantType(preGrantType);
        }
        clientConfig.setAccessTokenExpireInSeconds(tenantClientInfo.getAccessTokenExpireInSeconds());
        clientConfig.setPreAuthExpireInSeconds(tenantClientInfo.getPreAuthExpireInSeconds());
        clientConfig.setAccessTokenTransitionInSeconds(tenantClientInfo.getAccessTokenTransitionInSeconds());
        return clientConfig;
    }
}
