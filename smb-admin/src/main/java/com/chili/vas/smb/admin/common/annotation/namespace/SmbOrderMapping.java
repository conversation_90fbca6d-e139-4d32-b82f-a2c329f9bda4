package com.chili.vas.smb.admin.common.annotation.namespace;

import com.chili.vas.smb.admin.common.annotation.constant.NamespaceConstant;
import com.wftk.namespace.spring.boot.autoconfigure.annotation.NamespaceRequestMapping;
import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

/**
 * SmbOrderMapping 接口
 *
 * <AUTHOR>
 * @since 2025/6/19 18:48
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@RestController
@NamespaceRequestMapping
public @interface SmbOrderMapping {
    @AliasFor(annotation = NamespaceRequestMapping.class)
    String namespace() default NamespaceConstant.SMB_ORDER;

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] value() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] path() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    RequestMethod[] method() default {};
}
