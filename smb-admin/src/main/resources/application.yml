spring:
  application:
    name: smb-admin
  profiles:
    active: dev
  main:
    allow-circular-references: true

server:
  port: 8200


mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

management:
  server:
    port: 8201
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /management
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always

config:
  auth:
    filter:
      ignore-patterns:
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /admin/auth/**
      enable: true
    validation:
      client:
        enable: false
      account:
        enable: false
    client:
      pre-auth-expire-in-seconds: 30000
      pre-auth-duration-in-seconds: 1
  signature:
    filter:
      ignored-patterns:
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /admin/auth/**

  mybatis:
    tenant:
      enabled: false

  pageable:
    allow-page-size-over-flow: true
    max-page-size: 100