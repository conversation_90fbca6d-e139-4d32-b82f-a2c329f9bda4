package com.chili.vas.smb.portal.converter;

import java.time.LocalDate;
import java.util.Map;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderConsignDTO;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper(componentModel = "spring")
public interface SmbEquityGrantConverter {

    @Mapping(target = "grantDate", source = "paramsMap", qualifiedByName = "mapToLocalDate")
    SmbEquityGrantDTO toSmbEquityGrantDTO(Map<String, String> paramsMap);

    SmbEquityGrantDTO consignDTOtoSmbEquityGrantDTO(SmbOrderConsignDTO smbOrderConsignDTO);
    
    @Named("mapToLocalDate")
    default LocalDate mapToLocalDate(Map<String, String> paramsMap) {
        if (paramsMap == null || StrUtil.isBlank(paramsMap.get("grantDate"))) {
            return null;
        }
        return LocalDate.parse(paramsMap.get("grantDate").trim());
    }
}