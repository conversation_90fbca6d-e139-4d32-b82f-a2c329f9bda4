package com.chili.vas.smb.portal.schedule.smb;

import com.chili.vas.smb.biz.service.FeishuMessageService;
import com.chili.vas.smb.portal.common.constants.MessageTempCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.job.SystemJob;
import com.chili.vas.smb.biz.job.context.SystemJobContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public abstract class SmbJob implements SystemJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    protected final SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    private FeishuMessageService feishuMessageService;

    public SmbJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        this.smbApplyFileBatchService = smbApplyFileBatchService;
    }

    public void execute(SystemJobContext context) {
        logger.info("SmbJob job start...");
        SmbApplyFileBatch smbApplyFileBatch = null;
        try {
            smbApplyFileBatch = (SmbApplyFileBatch) context.getParamMap().get("smbApplyBatchInfo");
            if (smbApplyFileBatch == null) {
                Long bizId = (Long) context.getParamMap().get("bizId");
                if (bizId == null) {
                    throw new IllegalArgumentException("invalid bizId: " + bizId);
                }
                smbApplyFileBatch = smbApplyFileBatchService.getById(bizId);
                if (smbApplyFileBatch == null) {
                    throw new IllegalArgumentException("invalid bizId: " + bizId);
                }
            }
            doExecute(smbApplyFileBatch);
        } catch (Exception e) {
            logger.error("execute job error", e);
            sendFeishuMessage(smbApplyFileBatch,e);
            context.addException(getJobCode(), e);

        }
    }

    protected abstract void doExecute(SmbApplyFileBatch smbApplyFileBatch);


    /**
     *  失败发送飞书告警
     * @param smbApplyFileBatch
     * @param e
     */
    private void sendFeishuMessage(SmbApplyFileBatch smbApplyFileBatch,  Exception e) {
        try {
            logger.info("execute job failed feishu start ....");
            Map<String,Object> params = new HashMap<>();
            if (Objects.nonNull(smbApplyFileBatch)){
                params.put("fileName", smbApplyFileBatch.getFileName());
                params.put("batchNo", smbApplyFileBatch.getBatchNo());
            }
            params.put("jobCode",  getJobCode());
            params.put("status","失败");
            params.put("titleColor","red");
            params.put("remark",e.getMessage());
            feishuMessageService.send(MessageTempCode.EXCEL_BUSINESS_JOB,params);
            logger.info("execute job failed feishu end....");
        }catch (Exception ex){
            logger.error("sendFeishuMessage error", ex);
        }
    }
}
