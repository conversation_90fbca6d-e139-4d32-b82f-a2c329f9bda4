package com.chili.vas.smb.portal.service;

import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.biz.dto.SmbEquityStatusQueryDTO;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderQueryDTO;

/**
 * <AUTHOR>
 * @createDate 2025/5/28 15:30
 */
public interface PortalSmbOrderService {

     /**
      * 分页写入
      * @param total
      * @param batchNo
      */
     void pageWrite(int total, String batchNo);

     /**
      * 激活
      *
      * @param smbEquityGrantDTO
      */
     void activate(SmbEquityGrantDTO smbEquityGrantDTO);

     /**
      * 寄售
      * @param smbEquityGrantDTO
      * @return
      */
     String consign(SmbEquityGrantDTO smbEquityGrantDTO);

     /**
      * 取消
      *
      * @param smbEquityGrantDTO
      */
     void cancel(SmbEquityGrantDTO smbEquityGrantDTO);

     /**
      * 查询
      * 0.待激活 --> 00.待激活
      * 1.已激活  --> 06.激活
      * 2.寄售中  --> 06.激活
      * 3.寄售成功  --> 01.寄售
      * 11.寄售失败  --> 06.激活
      * 12.已取消  --> 07.发放取消
      * @param smbEquityStatusQueryDTO
      * @return
      */
     SmbOrderQueryDTO queryStatus(SmbEquityStatusQueryDTO smbEquityStatusQueryDTO);

     /**
      * 创建并激活
      * @param smbEquityGrantDTO
      * @return
      */
     SmbOrder createAndActivate(SmbEquityGrantDTO smbEquityGrantDTO);
}
