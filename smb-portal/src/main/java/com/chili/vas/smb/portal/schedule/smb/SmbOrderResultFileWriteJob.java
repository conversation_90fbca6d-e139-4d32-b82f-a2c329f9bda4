package com.chili.vas.smb.portal.schedule.smb;

import cn.hutool.core.util.IdUtil;
import com.chili.vas.smb.biz.dto.SmbOrderResultDetailDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 文件创建
 * @createDate 2025/5/26 17:57
 */
@Slf4j
@Component
public class SmbOrderResultFileWriteJob extends SmbJob {

    @Autowired
    SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    SmbApplyFileBatchDetailService smbApplyFileBatchDetailService;

    @Autowired
    SmbProperties smbProperties;

    @Autowired
    SmbManager smbManager;

    public SmbOrderResultFileWriteJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }


    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {
        String batchNo = smbApplyFileBatch.getBatchNo();

        // 定义格式 (yyyyMMdd)
        String fileName;
        if (smbApplyFileBatch.getFileName().contains("_equity_apply.txt")) {
            fileName = smbApplyFileBatch.getFileName().replace("_equity_apply.txt", "_equity_result.txt");
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            fileName = smbManager.getChiliChannelId() + "_" + smbManager.getChiliMerchantId() + "_"
                    +  smbApplyFileBatch.getTransDate().format(formatter) + "_" + IdUtil.nanoId(6)
                    + "_equity_result.txt";
        }

        File file = createFile(getLocalFilePath(fileName));

        try (BufferedWriter bw = new BufferedWriter(new FileWriter(file, true))) {

            int pageNumber = 0;
            int pageSize = 500;

            List<SmbOrderResultDetailDTO> pageData = smbApplyFileBatchDetailService.getSmbOrderResultDetailDTOList(batchNo, pageNumber * pageSize, pageSize);
            if (pageData.isEmpty()) {
                throw new SmbJobExecuteException("该批次订单不存在");
            }

            // 写入文件头 订单数量
            String totalCount = String.valueOf(smbApplyFileBatch.getTotalCount());
            bw.write(totalCount);
            bw.newLine();

            writePageData(bw, pageData);

            if (pageData.size() == pageSize) {
                pageNumber++;
                while (true) {
                    pageData = smbApplyFileBatchDetailService.getSmbOrderResultDetailDTOList(batchNo, pageNumber * pageSize, pageSize);
                    if (pageData.isEmpty()) {
                        break;
                    }
                    writePageData(bw, pageData);
                    pageNumber++;
                }
            }
        } catch (Exception e) {
            throw new SmbJobExecuteException("writePageData error", e);
        }

        smbApplyFileBatchService.updateResultFileNameByBatchNo(batchNo, fileName);
    }


    /**
     * 创建文件
     */
    protected File createFile(String path) {
        Path filePath = Paths.get(path);

        try {
            // 确保目录存在
            Files.createDirectories(filePath.getParent());

            // 创建空文件（自动覆盖已存在文件）
            return Files.createFile(filePath).toFile();
        } catch (FileAlreadyExistsException e) {
            // 处理文件已存在的情况（理论上不会发生，因createFile会覆盖）
            try {
                Files.delete(filePath);
                return Files.createFile(filePath).toFile();
            } catch (IOException ex) {
                throw new SmbJobExecuteException("文件覆盖失败: " + path, ex);
            }
        } catch (IOException e) {
            throw new SmbJobExecuteException("文件操作失败: " + path, e);
        }
    }

    private void writePageData(BufferedWriter bw, List<SmbOrderResultDetailDTO> pageData) throws IOException, IllegalAccessException {
        // 定义格式 (yyyyMMdd)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        for (SmbOrderResultDetailDTO smbOrderResultDetail : pageData) {
            List<String> allPropertyValues = new ArrayList<>();
            // 设置属性值
            allPropertyValues.add(smbOrderResultDetail.getDepositSerno() == null ? "" : smbOrderResultDetail.getDepositSerno());
            allPropertyValues.add(fenToYuanStr(smbOrderResultDetail.getEquityAmount()));
            allPropertyValues.add(fenToYuanStr(smbOrderResultDetail.getCustomerAmount()));
            allPropertyValues.add(fenToYuanStr(smbOrderResultDetail.getPartnerAmount()));
            allPropertyValues.add(smbOrderResultDetail.getGrantDate() == null ? "" : smbOrderResultDetail.getGrantDate().format(formatter));
            allPropertyValues.add(smbOrderResultDetail.getCode() == null ? "" : smbOrderResultDetail.getCode());
            allPropertyValues.add(smbOrderResultDetail.getFailDesc() == null ? "" : smbOrderResultDetail.getFailDesc());
            allPropertyValues.add(smbOrderResultDetail.getSecret() == null ? "" : smbOrderResultDetail.getSecret());

            String valueFormat = StringUtils.join(allPropertyValues, "|");
            bw.write(valueFormat);
            bw.newLine();
            bw.flush();
        }
    }

    private String fenToYuanStr(Integer amt) {
        if (amt == null) {
            return "";
        }
        return new BigDecimal(amt).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
    }


    @Override
    public String getJobCode() {
        return "smb_order_result_file_write";
    }


    protected String getLocalFilePath(String fileName) {
        if (StrUtil.endWith(smbProperties.getTempPath(), "/")) {
            return smbProperties.getTempPath() + fileName;
        }
        return smbProperties.getTempPath() + "/" + fileName;
    }


}
