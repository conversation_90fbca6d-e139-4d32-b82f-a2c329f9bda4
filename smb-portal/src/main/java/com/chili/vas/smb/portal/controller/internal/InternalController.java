package com.chili.vas.smb.portal.controller.internal;

import cn.hutool.core.util.StrUtil;

import com.chili.vas.smb.biz.common.enums.SmbOrderSourceEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.biz.util.RandomUtil;
import com.chili.vas.smb.portal.common.annotation.namespace.InternalMapping;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderConsignDTO;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderCreateDTO;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import com.chili.vas.smb.portal.service.PortalSmbOrderService;
import com.chili.vas.smb.portal.converter.SmbEquityGrantConverter;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @createDate 2025/6/26 14:47
 */
@Slf4j
@InternalMapping
public class InternalController {

    @Autowired
    IdGenerator idGenerator;

    @Autowired
    private SmbOrderService smbOrderService;

    @Autowired
    PortalSmbOrderService portalSmbOrderService;

    @Autowired
    PortalConsignOrderService portalConsignOrderService;

    @Autowired
    SmbEquityGrantConverter smbEquityGrantConverter;

    @PostMapping("/createSmbOrder")
    public ApiResult<String> createSmbOrder(@RequestBody @Validated SmbOrderCreateDTO smbOrderCreateDTO){
        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);

        SmbOrder smbOrderData = smbOrderService.getOneByDepositSerno(smbOrderCreateDTO.getDepositSerno());
        if (smbOrderData != null){
            throw new BusinessException("订单已存在");
        }
        SmbOrder smbOrder = new SmbOrder();
        smbOrder.setId(idGenerator.nextId());
        smbOrder.setDeleted(0);
        smbOrder.setActivateTime(now);
        smbOrder.setCreateTime(now);
        smbOrder.setUpdateTime(now);
        smbOrder.setStatus(SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue());
        smbOrder.setSecret(RandomUtil.randomStr(16));
        smbOrder.setOrderNo(idGenerator.nextIdStr());
        smbOrder.setBatchNo( idGenerator.nextIdStr() );
        smbOrder.setSource(SmbOrderSourceEnum.FILE_IMPORT.getValue());

        smbOrder.setDepositSerno( smbOrderCreateDTO.getDepositSerno() );
        smbOrder.setEquityAmount( smbOrderCreateDTO.getEquityAmount() );
        smbOrder.setCustomerAmount( smbOrderCreateDTO.getCustomerAmount() );
        smbOrder.setPartnerAmount( smbOrderCreateDTO.getPartnerAmount() );
        smbOrder.setGrantDate( smbOrderCreateDTO.getGrantDate() );
        smbOrder.setAccountName( smbOrderCreateDTO.getAccountName() );
        smbOrder.setAccountNo( smbOrderCreateDTO.getAccountNo() );
        smbOrder.setBankAccountNum( smbOrderCreateDTO.getBankAccountNum() );
        smbOrder.setBankAccountName( smbOrderCreateDTO.getBankAccountName() );
        smbOrderService.save(smbOrder);
        return ApiResult.ok(smbOrder.getOrderNo());
    }


    @PostMapping("/doConsign")
    public ApiResult<String> doConsign(@RequestBody @Validated SmbOrderConsignDTO smbOrderConsignDTO){
        // 寄售
        SmbEquityGrantDTO smbEquityGrantDTO = smbEquityGrantConverter.consignDTOtoSmbEquityGrantDTO(smbOrderConsignDTO);
        String consignOrderNo = portalSmbOrderService.consign(smbEquityGrantDTO);
        if(StrUtil.isNotBlank(consignOrderNo)){
            ConsignOrder consignOrder = portalConsignOrderService.doConsignDraw(consignOrderNo);
            if(consignOrder != null){
                portalConsignOrderService.sendQunqiuHttpConsign(consignOrder);
            }
        }else {
            log.info("consignOrderNo is null");
        }
        return ApiResult.ok();
    }


}
