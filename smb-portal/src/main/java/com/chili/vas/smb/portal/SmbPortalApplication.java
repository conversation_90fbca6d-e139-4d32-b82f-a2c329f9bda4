package com.chili.vas.smb.portal;

import com.wftk.file.manager.spring.boot.autoconfigure.oss.EnableOSSClient;
import com.wftk.signature.spring.boot.autoconfigure.EnableSignatureFilter;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = { "com.chili.vas.smb"})
@MapperScan("com.chili.vas.smb.**.mapper")
@EnableScheduling
@EnableAsync
@EnableOSSClient
//@EnableSignatureFilter
public class SmbPortalApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmbPortalApplication.class, args);
    }

}
