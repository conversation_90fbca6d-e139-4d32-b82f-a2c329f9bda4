package com.chili.vas.smb.portal.schedule.smb.file;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
public class SmbApplyFileBatchDetailTxtFileReader {

    public int BATCH_COUNT = 500;

    private final List<SmbApplyFileBatchDetail> cachedDataList = new ArrayList<>();

    /**
     * 文件数据格式：
     * 存入编号|权益金额|客户收益|合作方报酬|发放日期|收款账户|收款账户名称|收款方账户开户行号|收款方账户开户行名
     * @param line
     * @return
     */
    private SmbApplyFileBatchDetail parseLine(String line) {
        // 对每一行的数据进行处理
        return setBatchConsignOrderDetail( line);
    }

    private SmbApplyFileBatchDetail setBatchConsignOrderDetail(String result) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        String[] order = result.split("\\|");
        SmbApplyFileBatchDetail smbApplyFileBatchDetail = new SmbApplyFileBatchDetail();
        smbApplyFileBatchDetail.setDepositSerno(order[0]);
        smbApplyFileBatchDetail.setEquityAmount(strYuanToFen(order[1]));
        smbApplyFileBatchDetail.setCustomerAmount(strYuanToFen(order[2]));
        smbApplyFileBatchDetail.setPartnerAmount(strYuanToFen(order[3]));
        smbApplyFileBatchDetail.setGrantDate(LocalDate.parse(order[4], formatter));
        smbApplyFileBatchDetail.setAccountNo(order[5]);
        smbApplyFileBatchDetail.setAccountName(order[6]);
        smbApplyFileBatchDetail.setBankAccountNum(order[7]);
        smbApplyFileBatchDetail.setBankAccountName(order[8]);
        return smbApplyFileBatchDetail;
    }

    public void doReadFile(String filePath, Consumer<String> fileHeadConsumer, Consumer<List<SmbApplyFileBatchDetail>> consumer) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            // 获取文件头 --- 订单数量
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new RuntimeException("文件内容为空");
            }
            // 处理文件头
            fileHeadConsumer.accept(headerLine);

            String line;
            while ((line = reader.readLine()) != null) {
                log.info("smb.apply.file line: {}",line);
                SmbApplyFileBatchDetail batchConsignOrderDetail = parseLine(line);
                log.info("smb.apply.file 解析到一条数据: {}",batchConsignOrderDetail);

                cachedDataList.add(batchConsignOrderDetail);
                if (cachedDataList.size() == BATCH_COUNT) {
                    // 处理批次详情集合
                    consumer.accept(cachedDataList);
                    cachedDataList.clear();
                }
            }
            // 处理批次详情集合
            consumer.accept(cachedDataList);
            cachedDataList.clear();
        } catch (Exception e){
            log.error("smb file apply doReadFile error.",e);
            throw new RuntimeException(e);
        }
    }

    private Integer strYuanToFen(String strAmt){
        if(StrUtil.isBlank(strAmt)){
            return 0;
        }
        return new BigDecimal(strAmt).multiply(new BigDecimal("100")).intValue();
    }

}
