package com.chili.vas.smb.portal.schedule.smb;

import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.portal.common.enums.SmbOrderValidationStatusEnum;
import com.chili.vas.smb.portal.service.PortalSmbOrderService;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 苏商订单写入
 */
@Slf4j
@Component
public class SmbOrderWriteJob extends SmbJob {

    @Autowired
    SmbApplyFileBatchDetailService smbApplyFileBatchDetailService;

    @Autowired
    PortalSmbOrderService portalSmbOrderService;

    @Autowired
    SmbOrderService smbOrderService;
    public SmbOrderWriteJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }

    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {
        String batchNo = smbApplyFileBatch.getBatchNo();
        log.info("order write job start, batchNo: {}", batchNo);
        // 校验数量
        Integer total = smbApplyFileBatchDetailService.totalByBatchNo(batchNo, SmbOrderValidationStatusEnum.SUCCESS.getCode());
        if (total == 0) {
            throw new BusinessException("可转换订单数量为空");
        }

        //默认写入的deleted为1
        portalSmbOrderService.pageWrite(total, batchNo);

        //校验订单数量
        Integer batchOrderCount = smbOrderService.countByBatchNo(batchNo, true);
        if(!total.equals(batchOrderCount)){
            log.warn("order maybe haven't completed, rollback it. batchNo: {}", batchNo);
            smbOrderService.deleteByBatchNo(batchNo);
        } else {
            //更新deleted为0
            smbOrderService.updateDeletedByBatchNo(batchNo,0);
        }
        log.info("order write job end, batchNo: {}", batchNo);
    }

    @Override
    public String getJobCode() {
        return "smb_order_write";
    }


}
