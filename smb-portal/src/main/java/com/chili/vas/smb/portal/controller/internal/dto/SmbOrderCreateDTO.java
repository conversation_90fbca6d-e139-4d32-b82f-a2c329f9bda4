package com.chili.vas.smb.portal.controller.internal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @createDate 2025/6/26 15:15
 */
@Data
public class SmbOrderCreateDTO {

    /**
     * 苏商存入编号
     */
    @NotBlank(message = "苏商存入编号不能为空")
    private String depositSerno;


    /**
     * 权益金额
     */
    @NotNull(message = "权益金额不能为空")
    private Integer equityAmount;

    /**
     * 客户收益
     */
    @NotNull(message = "客户收益不能为空")
    private Integer customerAmount;

    /**
     * 合作方报酬
     */
    @NotNull(message = "合作方报酬不能为空")
    private Integer partnerAmount;

    /**
     * 发放时间
     */
    @NotNull(message = "发放时间不能为空")
    private LocalDate grantDate;

    /**
     * 收款名称
     */
    @NotBlank(message = "收款名称不能为空")
    private String accountName;

    /**
     * 收款账户
     */
    @NotBlank(message = "收款账户不能为空")
    private String accountNo;

    /**
     * 开户行号
     */
    @NotBlank(message = "开户行号不能为空")
    private String bankAccountNum;

    /**
     * 开户行名
     */
    @NotBlank(message = "开户行名不能为空")
    private String bankAccountName;

}
