package com.chili.vas.smb.portal.service.impl;

import com.chili.vas.smb.biz.common.constant.JobConstant;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderWriteOffRecordStatusEnum;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderWriteOffRecord;
import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
import com.chili.vas.smb.biz.service.JobService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.biz.service.SmbOrderWriteOffRecordService;
import com.chili.vas.smb.portal.service.PortalSmbBusinessService;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 11:12
 */
@Slf4j
@Service
public class PortalSmbBusinessServiceImpl implements PortalSmbBusinessService {

    @Autowired
    SmbOrderService smbOrderService;

    @Autowired
    SmbOrderWriteOffRecordService smbOrderWriteOffRecordService;

    @Autowired
    JobService jobService;

    @Autowired
    SmbManager smbManager;

    @Override
    public void notifyToSmb(String smbOrderNo) throws Exception {
        log.info("[notifyToSmb]smb orderNo {}", smbOrderNo);
        SmbOrder smbOrder = smbOrderService.getByOrderNo(smbOrderNo);
        if(smbOrder == null){
            log.error("[notifyToSmb]smb order is not exist order no is {}", smbOrderNo);
            return;
        }

        // 创建核销记录
/*        SmbOrderWriteOffRecord writeOffRecord = new SmbOrderWriteOffRecord();
        writeOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
        writeOffRecord.setDepositSerno(smbOrder.getDepositSerno());
        writeOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.WAIT.getValue());
        writeOffRecord.setManualFlag(false);
        smbOrderWriteOffRecordService.save(writeOffRecord);*/

        SmbOrderWriteOffRecord writeOffRecord = smbOrderWriteOffRecordService.findOneBySmbOrderNo(smbOrderNo);
        if (writeOffRecord == null){
            writeOffRecord = new SmbOrderWriteOffRecord();
            writeOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
            writeOffRecord.setDepositSerno(smbOrder.getDepositSerno());
            writeOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.WAIT.getValue());
            writeOffRecord.setManualFlag(false);
            smbOrderWriteOffRecordService.save(writeOffRecord);
        }else{
            writeOffRecord.setSmbOrderNo(smbOrder.getOrderNo());
            writeOffRecord.setDepositSerno(smbOrder.getDepositSerno());
            writeOffRecord.setStatus(SmbOrderWriteOffRecordStatusEnum.WAIT.getValue());
            writeOffRecord.setManualFlag(false);
            smbOrderWriteOffRecordService.updateById(writeOffRecord);
        }


        // 创建调度任务
        jobService.initJob(JobConstant.SceneCode.SMB_ORDER_WRITE_OFF, smbOrder.getId());

//        Map map = sendNotify(smbOrder);
//        Map payload = (Map) map.get("payload");
//        if(payload != null){
//            String rtnCode = (String) payload.get("rtnCode");
//            if("0000".equals(rtnCode)){
//                smbOrderService.updateWriteOffStatusByOrderNo(smbOrder.getOrderNo(), SmbOrderWriteOffStatusEnum.WRITE_OFF_YES.getValue(), LocalDateTime.now());
//            }
//        }
    }

    private Map sendNotify(SmbOrder smbOrder) throws Exception {
        String serilNo = UUID.randomUUID().toString().replace("-", "");
        String transStatus;
        if (Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue())) {
            transStatus = "02";
        } else if (Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())) {
            transStatus = "03";
        }else {
            throw new BusinessException("苏商状态异常，不能执行回调");
        }
        // 定义格式 (yyyyMMdd)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map payload = new HashMap<>();
        payload.put("merchantId", smbManager.getPoyanMerchantId());
        payload.put("depositSerno", smbOrder.getDepositSerno());
        payload.put("grantDate", smbOrder.getGrantDate().format(formatter));
        payload.put("equityCode", smbOrder.getSecret());
        payload.put("equityAmt", fenToYuanStr(smbOrder.getEquityAmount()));
        payload.put("partnerAmt", fenToYuanStr(smbOrder.getPartnerAmount()));
        payload.put("customAmt", fenToYuanStr(smbOrder.getCustomerAmount()));
        payload.put("transStatus", transStatus);
        payload.put("partnerRtnCode", "0000");
        payload.put("partnerRtnDesc", "成功");
        Map map = smbManager.fundReceivedCallback(serilNo, payload);
        return map;
    }

    private String fenToYuanStr(Integer amt) {
        if (amt == null) {
            return "";
        }
        return new BigDecimal(amt).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
    }


}
