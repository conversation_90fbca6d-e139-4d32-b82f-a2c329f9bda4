package com.chili.vas.smb.portal.converter;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


/**
 * <AUTHOR>
 * @createDate 2025/6/5 14:29
 */
@Mapper(componentModel = "spring")
public interface ConsignOrderConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "orderNo", ignore = true)
    @Mapping(target = "smbOrderNo", source = "orderNo")
    @Mapping(target = "amount", source = "customerAmount")
    ConsignOrder toConsignOrder(SmbOrder smbOrder);

}
