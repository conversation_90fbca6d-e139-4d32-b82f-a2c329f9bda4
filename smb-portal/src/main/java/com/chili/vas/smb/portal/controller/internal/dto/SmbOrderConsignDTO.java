package com.chili.vas.smb.portal.controller.internal.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2025/6/26 15:43
 */
@Data
public class SmbOrderConsignDTO {

    /**
     * 苏商存入编号
     */
    @NotBlank(message = "苏商存入编号不能为空")
    private String depositSerno;

    /**
     * 回款账户开户行名
     */
    @NotBlank(message = "回款账户开户行名不能为空")
    private String collectBankName;

    /**
     * 回款账户联行号
     */
    @NotBlank(message = "回款账户联行号不能为空")
    private String collectBankCode;

    /**
     * 回款账户名称
     */
    @NotBlank(message = "回款账户名称不能为空")
    private String collectAcctName;

    /**
     * 回款账户
     */
    @NotBlank(message = "回款账户不能为空")
    private String collectAcctNo;

}
