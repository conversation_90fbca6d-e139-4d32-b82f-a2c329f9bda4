package com.chili.vas.smb.portal.controller.callback;

import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentCallbackInput;
import com.chili.vas.smb.portal.common.annotation.namespace.CallbackMapping;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @createDate 2025/6/11 10:04
 */
@Slf4j
@CallbackMapping("qunqiu")
public class QunqiuCallbackController {

    @Autowired
    PortalConsignOrderService portalConsignOrderService;

    @PostMapping("/consign")
    public void receive(@RequestBody PaymentCallbackInput paymentCallbackInput){
        log.info("qunqiu callback result is {}", paymentCallbackInput);
        portalConsignOrderService.receiveQunqiuCallBack(paymentCallbackInput);
    }


}
