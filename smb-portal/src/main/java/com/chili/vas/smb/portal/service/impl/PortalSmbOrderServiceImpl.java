package com.chili.vas.smb.portal.service.impl;

import com.chili.vas.smb.biz.common.enums.ConsignOrderFlagCardEnum;
import com.chili.vas.smb.biz.common.enums.ConsignOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderOperateSceneEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderSourceEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.biz.dto.SmbEquityStatusQueryDTO;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderOperateRecord;
import com.chili.vas.smb.biz.ext.smb.constant.SmbConstant;
import com.chili.vas.smb.biz.service.ConsignOrderService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.chili.vas.smb.biz.service.SmbOrderOperateRecordService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.biz.util.RandomUtil;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderQueryDTO;
import com.chili.vas.smb.portal.converter.ConsignOrderConverter;
import com.chili.vas.smb.portal.converter.SmbOrderConverter;
import com.chili.vas.smb.portal.service.PortalSmbOrderService;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/5/28 15:31
 */
@Slf4j
@Service
public class PortalSmbOrderServiceImpl implements PortalSmbOrderService {

    @Autowired
    SmbApplyFileBatchDetailService smbApplyFileBatchDetailService;

    @Autowired
    SmbOrderService smbOrderService;

    @Autowired
    SmbOrderOperateRecordService smbOrderOperateRecordService;

    @Autowired
    ConsignOrderService consignOrderService;

    @Autowired
    SmbOrderConverter smbOrderConverter;

    @Autowired
    ConsignOrderConverter consignOrderConverter;

    @Autowired
    IdGenerator idGenerator;

    @Override
    public void pageWrite(int total, String batchNo) {
        int offset = 0;
        int batchSize = 500;

        while (offset < total){
            // 获取批次订单数据
            List<SmbApplyFileBatchDetail> fileBatchDetails = smbApplyFileBatchDetailService
                    .pageByBatchNoAndResultCode(batchNo, "0000", offset, batchSize);
            if(CollUtil.isEmpty(fileBatchDetails)){
                log.info("empty fileBatchDetails, ignore...");
                return;
            }
            offset += batchSize;

            // 转换为苏商订单
            List<SmbOrder> smbOrders = smbOrderConverter.toSmbOrder(fileBatchDetails);

            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            for (SmbOrder smbOrder : smbOrders) {
                smbOrder.setId(idGenerator.nextId());
                smbOrder.setDeleted(1);
                smbOrder.setCreateTime(now);
                smbOrder.setUpdateTime(now);
                smbOrder.setStatus(SmbOrderStatusEnum.ACTIVATE_WAITING.getValue());
                smbOrder.setSecret(RandomUtil.randomStr(16));
                smbOrder.setOrderNo(idGenerator.nextIdStr());
                smbOrder.setSource(SmbOrderSourceEnum.FILE_IMPORT.getValue());
            }

            //初始化寄售订单
            smbOrderService.insertBatch(smbOrders);

        }
    }

    @Transactional
    @Override
    public void activate(SmbEquityGrantDTO smbEquityGrantDTO) {
        SmbOrder smbOrder = smbOrderService.getByDepositSernoAndSecret(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode());
        if(smbOrder == null){
            throw new BusinessException("订单不存在");
        }

        Integer updated = smbOrderService.updateStatusByDepositSernoAndSecret(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode(),
                SmbOrderStatusEnum.ACTIVATE_WAITING.getValue(), SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue());
        if(updated < 1){
            throw new BusinessException("订单激活失败");
        }

        // 修改激活时间
        SmbOrder updateSmbOrder = new SmbOrder();
        updateSmbOrder.setId(smbOrder.getId());
        updateSmbOrder.setActivateTime(LocalDateTime.now());
        smbOrderService.updateById(updateSmbOrder);

        // 新增操作记录
        SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
        smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
        smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue());
        smbOrderOperateRecord.setRequestSerial(smbEquityGrantDTO.getChannelSerialNo());
        smbOrderOperateRecord.setParams(JSONObject.getInstance().toJSONString(smbEquityGrantDTO));
        smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.EQUITY_GRANT_ACTIVATE.getValue());
        smbOrderOperateRecordService.save(smbOrderOperateRecord);

    }

    @Transactional
    @Override
    public String consign(SmbEquityGrantDTO smbEquityGrantDTO) {
        SmbOrder smbOrder = smbOrderService.getByDepositSernoAndSecret(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode());
        if(smbOrder == null){
            throw new BusinessException("订单不存在");
        }

        // 发生了寄售行为后不能直接响应为失败
        if(Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue())){
            log.info("订单正在寄售中. {}",smbOrder);
            return null;
        }
        if(Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue())){
            log.info("订单已寄售成功. {}",smbOrder);
            return null;
        }
        if(Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())){
            log.info("订单已寄售失败. {}",smbOrder);
            return null;
        }

        Integer updated = smbOrderService.updateStatusByDepositSernoAndSecret(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode(),
                SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue());
        if(updated < 1){
            throw new BusinessException("订单寄售失败");
        }

        // 新增操作记录
        SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
        smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
        smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue());
        smbOrderOperateRecord.setRequestSerial(smbEquityGrantDTO.getChannelSerialNo());
        smbOrderOperateRecord.setParams(JSONObject.getInstance().toJSONString(smbEquityGrantDTO));
        smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.EQUITY_GRANT_CONSIGN.getValue());
        smbOrderOperateRecordService.save(smbOrderOperateRecord);

        // 创建寄售订单
        ConsignOrder consignOrder = consignOrderConverter.toConsignOrder(smbOrder);
        consignOrder.setOrderNo(idGenerator.nextIdStr());
        consignOrder.setStatus(ConsignOrderStatusEnum.INIT.getValue());
        consignOrder.setFlagCard(ConsignOrderFlagCardEnum.PUBLIC.getValue());
        consignOrder.setBankAccountName(smbEquityGrantDTO.getCollectBankName());
        consignOrder.setBankAccountNum(smbEquityGrantDTO.getCollectBankCode());
        consignOrder.setName(smbEquityGrantDTO.getCollectAcctName());
        consignOrder.setBankAccountNo(smbEquityGrantDTO.getCollectAcctNo());
        consignOrderService.save(consignOrder);

        return consignOrder.getOrderNo();
    }

    @Transactional
    @Override
    public void cancel(SmbEquityGrantDTO smbEquityGrantDTO) {
        SmbOrder smbOrder = smbOrderService.getByDepositSernoAndSecret(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode());
        if(smbOrder == null){
            throw new BusinessException("订单不存在");
        }

        Integer updated = smbOrderService.updateStatusByDepositSernoAndStatusList(smbEquityGrantDTO.getDepositSerno(), smbEquityGrantDTO.getEquityCode(), 
            List.of(SmbOrderStatusEnum.ACTIVATE_WAITING.getValue(),SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue()), SmbOrderStatusEnum.CANCEL.getValue());
        if(updated < 1){
            throw new BusinessException("订单取消失败");
        }

        // 修改取消时间
        SmbOrder updateSmbOrder = new SmbOrder();
        updateSmbOrder.setId(smbOrder.getId());
        updateSmbOrder.setCancelTime(LocalDateTime.now());
        smbOrderService.updateById(updateSmbOrder);

        // 新增操作记录
        SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
        smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
        smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CANCEL.getValue());
        smbOrderOperateRecord.setRequestSerial(smbEquityGrantDTO.getChannelSerialNo());
        smbOrderOperateRecord.setParams(JSONObject.getInstance().toJSONString(smbEquityGrantDTO));
        smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.EQUITY_GRANT_CANCEL.getValue());
        smbOrderOperateRecordService.save(smbOrderOperateRecord);
    }

    /**
     * 0.待激活 --> 00.待激活
     * 1.已激活  --> 06.激活
     * 2.寄售中  --> 06.激活
     * 3.寄售成功  --> 01.寄售
     * 11.寄售失败  --> 06.激活
     * 12.已取消  --> 07.发放取消
     * @param smbEquityStatusQueryDTO
     * @return
     */
    @Override
    public SmbOrderQueryDTO queryStatus(SmbEquityStatusQueryDTO smbEquityStatusQueryDTO) {
        SmbOrder smbOrder;
        // 行方告知的规则为：equityCode为空时，transSerno一定不为空；equityCode不为空时，transSerno值为空；
        if (StrUtil.isNotBlank(smbEquityStatusQueryDTO.getEquityCode())) {
            smbOrder = smbOrderService.getByDepositSernoAndSecret(smbEquityStatusQueryDTO.getDepositSerno(), smbEquityStatusQueryDTO.getEquityCode());
        } else {
            // 未携带equityCode时，transSerno不能为空
            if (StrUtil.isBlank(smbEquityStatusQueryDTO.getTransSerno())) {
                throw new IllegalArgumentException("transSerno不能为空");
            }
            smbOrder = smbOrderService.getByDepositSernoAndBatchNo(smbEquityStatusQueryDTO.getDepositSerno(), smbEquityStatusQueryDTO.getTransSerno());
        }
        if(smbOrder == null){
            throw new BusinessException("订单不存在");
        }

        String equityStatus;
        SmbOrderStatusEnum paymentStatusEnum = SmbOrderStatusEnum
                .getSmbOrderStatusEnumByValue(smbOrder.getStatus());
        // 寄售中 不代表寄售成功
        switch (Objects.requireNonNull(paymentStatusEnum)) {
            case ACTIVATE_WAITING -> equityStatus = SmbConstant.EquityQueryStatus.AWAIT_ACTIVE;
            case ACTIVATE_COMPLETE, CONSIGN_FAIL, CONSIGN_PROCESSING -> equityStatus = SmbConstant.EquityQueryStatus.ACTIVE;
            case CONSIGN_SUCCESS -> equityStatus = SmbConstant.EquityQueryStatus.CONSIGN;
            case CANCEL -> equityStatus = SmbConstant.EquityQueryStatus.CANCEL;
            default -> throw new BusinessException("未知的状态");
        }
        return new SmbOrderQueryDTO(smbOrder, equityStatus);
    }

    @Override
    public SmbOrder createAndActivate(SmbEquityGrantDTO smbEquityGrantDTO) {
        
        SmbOrder smbOrder = smbOrderConverter.dtoToSmbOrder(smbEquityGrantDTO);
        //此场景默认为激活成功
        smbOrder.setStatus(SmbOrderStatusEnum.ACTIVATE_COMPLETE.getValue());
        smbOrder.setActivateTime(LocalDateTime.now());
        smbOrder.setSecret(RandomUtil.randomStr(16));
        smbOrder.setOrderNo(idGenerator.nextIdStr());
        smbOrder.setSource(SmbOrderSourceEnum.API_CREATE.getValue());
        smbOrder.setBatchNo(smbEquityGrantDTO.getTransSerno());
        smbOrderService.save(smbOrder);
        return smbOrder;
    }


}
