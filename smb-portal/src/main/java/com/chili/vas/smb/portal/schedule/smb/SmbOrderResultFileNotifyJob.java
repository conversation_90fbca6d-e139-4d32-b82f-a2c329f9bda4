package com.chili.vas.smb.portal.schedule.smb;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * 结果文件通知
 * @createDate 2025/5/26 17:57
 */
@Slf4j
@Component
public class SmbOrderResultFileNotifyJob extends SmbJob {

    @Autowired
    SmbProperties smbProperties;

    @Autowired
    SmbManager smbManager;

    public SmbOrderResultFileNotifyJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }

    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {

        String serilNo = UUID.randomUUID().toString().replace("-", "");

        if(StrUtil.isNotBlank(smbApplyFileBatch.getResultFilePath())){
            // 定义格式 (yyyyMMdd)
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            Map payload = new HashMap<>();
            payload.put("merchantId",smbManager.getChiliMerchantId());
            payload.put("transDate",smbApplyFileBatch.getTransDate().format(formatter));
            payload.put("serviceNum",smbProperties.getServiceNum());
            payload.put("fileName",smbApplyFileBatch.getResultFileName());
            // filepath需要去掉文件名
            payload.put("filePath", smbApplyFileBatch.getResultFilePath().replace(smbApplyFileBatch.getResultFileName(),""));

            try {
                smbManager.resultFileCallback(serilNo, payload);
            }catch (Exception e){
                throw new SmbJobExecuteException("resultFileCallback error", e);
            }
        }else {
            log.info("smb_order_result_file_notify doExecute resultFilePath is null. {}",smbApplyFileBatch);
        }


    }

    @Override
    public String getJobCode() {
        return "smb_order_result_file_notify";
    }

}
