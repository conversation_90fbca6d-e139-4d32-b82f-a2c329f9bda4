package com.chili.vas.smb.portal.controller.openapi;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.dto.SmbApplyFileInfoDTO;
import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.biz.dto.SmbEquityStatusQueryDTO;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.ext.smb.constant.SmbConstant;
import com.chili.vas.smb.biz.service.JobService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.common.constant.JobConstant;
import com.chili.vas.smb.biz.common.validator.ProgrammaticValidator;
import com.chili.vas.smb.portal.common.annotation.namespace.OpenApiMapping;
import com.chili.vas.smb.portal.controller.internal.dto.SmbOrderQueryDTO;
import com.chili.vas.smb.portal.converter.SmbEquityGrantConverter;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import com.chili.vas.smb.portal.service.PortalSmbOrderService;
import com.snb.fsos.sdk.SnbSdk;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @createDate 2025/5/26 14:59
 */
@Slf4j
@OpenApiMapping
public class SmbController {

    @Autowired
    @Qualifier("chiliSnbSdk")
    private SnbSdk chiliSnbSdk;

    @Autowired
    @Qualifier("poyanSnbSdk")
    private SnbSdk poyanSnbSdk;

    @Autowired
    private SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    private ProgrammaticValidator programmaticValidator;

    @Autowired
    PortalSmbOrderService portalSmbOrderService;

    @Autowired
    PortalConsignOrderService portalConsignOrderService;

    @Autowired
    private JobService jobService;

    @Autowired
    private SmbEquityGrantConverter smbEquityGrantConverter;

    @PostMapping(path = SmbConstant.TransCode.NOTICE_OF_EQUITY_APPLY, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map receiveApplyFileNotice(@RequestParam Map<String, String> params) throws Exception {
        log.info("receiveApplyFileNotice params : {}", params);

        SmbApplyFileBatch smbApplyFileBatch = null;
        try {
            Map<String, String> payloadMap = this.chiliSnbSdk.receiveRequest(params);
            log.info("receiveApplyFileNotice payloadMap : {}", payloadMap);

            // 创建批次
            SmbApplyFileInfoDTO smbApplyFileInfoDTO = new SmbApplyFileInfoDTO();
            smbApplyFileInfoDTO.setSerialNo(params.get("channelSerialNo"));
            smbApplyFileInfoDTO.setFilePath(payloadMap.get("filePath"));
            // 处理所有类型的路径分隔符（/ 或 \）
            String fileName = null;
            if(StrUtil.isNotBlank(smbApplyFileInfoDTO.getFilePath())){
                fileName = smbApplyFileInfoDTO.getFilePath().replaceAll(".*[/\\\\]", "");
            }
            smbApplyFileInfoDTO.setFileName(fileName);
            String grantDateStr = payloadMap.get("transDate");
            LocalDate grantDate = null;
            if(StrUtil.isNotBlank(grantDateStr)){
                grantDate = LocalDate.parse(grantDateStr);
            }
            smbApplyFileInfoDTO.setTransDate(grantDate);


            programmaticValidator.validate(smbApplyFileInfoDTO);
            smbApplyFileBatch = smbApplyFileBatchService.create(smbApplyFileInfoDTO);
            // 创建调度任务
            jobService.initJob(JobConstant.SceneCode.CONSIGN, smbApplyFileBatch.getId());
        }catch (SQLException e){
            log.error("receiveApplyFileNotice error", e);
            return SmbReponseBuilders.fail(this.chiliSnbSdk, params.get("channelSerialNo"), new BusinessException("系统异常")).build();
        } catch (Exception e) {
            log.error("receiveApplyFileNotice error", e);
            return SmbReponseBuilders.fail(this.chiliSnbSdk, params.get("channelSerialNo"), e).build();
        }

        // 创建响应payload
        return SmbReponseBuilders.ok(this.chiliSnbSdk, params.get("channelSerialNo"))
                .add("finishTime", LocalDateTimeUtil.formatNormal(smbApplyFileBatch.getCreateTime()))
                .build();
    }


    /**
     * 驰丽权益状态查询
     * @param params
     * @return
     * @throws Exception
     */
    @PostMapping(path = SmbConstant.TransCode.QUERY_OF_EQUITY_STATUS, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map receiveEquityStatusQuery(@RequestParam Map<String, String> params) throws Exception {
        log.info("receiveEquityStatusQuery params : {}", params);

        SmbOrderQueryDTO result = null;

        try {
            Map payloadMap = this.chiliSnbSdk.receiveRequest(params);
            log.info("receiveEquityStatusQuery payloadMap : {}", payloadMap);

            // 获取参数以及校验参数
            String depositSerno = (String) payloadMap.get("depositSerno");
            LocalDate grantDate = null;
            String grantDateStr = (String) payloadMap.get("grantDate");
            if(StrUtil.isNotBlank(grantDateStr)){
                grantDate = LocalDate.parse(grantDateStr);
            }
            String equityCode = (String) payloadMap.get("equityCode");
            String transSerno = (String) payloadMap.get("transSerno");

            SmbEquityStatusQueryDTO smbEquityStatusQueryDTO = new SmbEquityStatusQueryDTO();
            smbEquityStatusQueryDTO.setDepositSerno(depositSerno);
            smbEquityStatusQueryDTO.setGrantDate(grantDate);
            smbEquityStatusQueryDTO.setEquityCode(equityCode);
            smbEquityStatusQueryDTO.setTransSerno(transSerno);

            programmaticValidator.validate(smbEquityStatusQueryDTO);
            result = portalSmbOrderService.queryStatus(smbEquityStatusQueryDTO);
        } catch (Exception e) {
            log.error("receiveEquityStatusQuery error", e);
            return SmbReponseBuilders.fail(this.chiliSnbSdk, params.get("channelSerialNo"), e).build();
        }

        // 创建响应payload
        return SmbReponseBuilders.ok(this.chiliSnbSdk, params.get("channelSerialNo"))
                .add("equityStatus", result.getConvertedStatus())
                .add("equityCode", result.getSmbOrder().getSecret())
                .build();
    }

    /**
     * 驰丽权益派发
     * @param params
     * @return
     * @throws Exception
     */
    @PostMapping(path = SmbConstant.TransCode.DISTRIBUTION_OF_BENEFITS, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map receiveEquityGrant(@RequestParam Map<String, String> params) throws Exception {
        log.info("receiveEquityGrant params : {}", params);

        try {
            Map<String, String> payloadMap = this.chiliSnbSdk.receiveRequest(params);
            log.info("receiveEquityGrant payloadMap : {}", payloadMap);

            SmbEquityGrantDTO smbEquityGrantDTO = smbEquityGrantConverter.toSmbEquityGrantDTO(payloadMap);
            smbEquityGrantDTO.setChannelSerialNo(params.get("channelSerialNo"));
            String transType = smbEquityGrantDTO.getTransType();

            if (SmbConstant.TransType.ACTIVE.equals(transType)) {
                // 激活
                programmaticValidator.validate(smbEquityGrantDTO, SmbEquityGrantDTO.ActiveGroup.class);
                portalSmbOrderService.activate(smbEquityGrantDTO);
            } else if (SmbConstant.TransType.CREATE_AND_ACTIVE.equals(transType)) {
                // 创建并激活
                programmaticValidator.validate(smbEquityGrantDTO, SmbEquityGrantDTO.CreateAndActiveGroup.class);
                SmbOrder smbOrder = portalSmbOrderService.createAndActivate(smbEquityGrantDTO);
                return SmbReponseBuilders.ok(this.chiliSnbSdk, params.get("channelSerialNo"))
                        .add("transStatus", "02")
                        .add("finishTime", LocalDateTimeUtil.formatNormal(LocalDateTime.now()))
                        .add("equityCode", smbOrder.getSecret())
                        .build();
            } else if (SmbConstant.TransType.CANCEL.equals(transType)) {
                // 取消
                programmaticValidator.validate(smbEquityGrantDTO, SmbEquityGrantDTO.CancelGroup.class);
                portalSmbOrderService.cancel(smbEquityGrantDTO);
            } else {
                throw new IllegalArgumentException("invalid transType. " + transType);
            }
        } catch (Exception e) {
            log.error("receiveEquityGrant error", e);
            return SmbReponseBuilders.fail(this.chiliSnbSdk, params.get("channelSerialNo"), e).build();
        }

        return SmbReponseBuilders.ok(this.chiliSnbSdk, params.get("channelSerialNo"))
                .add("transStatus", "02")
                .add("finishTime", LocalDateTimeUtil.formatNormal(LocalDateTime.now()))
                .build();
    }

    /**
     * 珀延权益寄售
     * @param params
     * @return
     */
    @PostMapping(path = SmbConstant.TransCode.POYAN_DISTRIBUTION_OF_BENEFITS, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map receivePoyanEquityGrant(@RequestParam Map<String, String> params) {
        log.info("receivePoyanEquityGrant params : {}", params);

        try {
            Map<String, String> payloadMap = this.poyanSnbSdk.receiveRequest(params);
            log.info("receivePoyanEquityGrant payloadMap : {}", payloadMap);

            String transType = payloadMap.get("transType");
            if (StrUtil.isBlank(transType) || !transType.equals(SmbConstant.TransType.CONSIGN)) {
                throw new IllegalArgumentException("invalid transType. " + transType);
            }
            SmbEquityGrantDTO smbEquityGrantDTO = smbEquityGrantConverter.toSmbEquityGrantDTO(payloadMap);
            smbEquityGrantDTO.setChannelSerialNo(params.get("channelSerialNo"));

            programmaticValidator.validate(smbEquityGrantDTO, SmbEquityGrantDTO.ConsignGroup.class);

            String consignOrderNo = portalSmbOrderService.consign(smbEquityGrantDTO);
            if(StrUtil.isNotBlank(consignOrderNo)){
                ConsignOrder consignOrder = portalConsignOrderService.doConsignDraw(consignOrderNo);
                if(consignOrder != null){
                    portalConsignOrderService.sendQunqiuHttpConsign(consignOrder);
                }
            }
        } catch (Exception e) {
            log.error("receivePoyanEquityGrant error", e);
            return SmbReponseBuilders.fail(this.poyanSnbSdk, params.get("channelSerialNo"), e).build();
        }

        return SmbReponseBuilders.ok(this.poyanSnbSdk, params.get("channelSerialNo"))
                .add("transStatus", "02")
                .add("finishTime", LocalDateTimeUtil.formatNormal(LocalDateTime.now()))
                .build();
    }


    /**
     * 珀岩权益状态查询
     * @param params
     * @return
     */
    @PostMapping(path = SmbConstant.TransCode.POYAN_QUERY_OF_EQUITY_STATUS, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map receivePoyanEquityStatusQuery(@RequestParam Map<String, String> params) {
        log.info("receivePoyanEquityStatusQuery params : {}", params);

        SmbOrderQueryDTO result = null;

        try {
            Map payloadMap = this.poyanSnbSdk.receiveRequest(params);
            log.info("receivePoyanEquityStatusQuery payloadMap : {}", payloadMap);

            // 获取参数以及校验参数
            String depositSerno = (String) payloadMap.get("depositSerno");
            LocalDate grantDate = null;
            String grantDateStr = (String) payloadMap.get("grantDate");
            if(StrUtil.isNotBlank(grantDateStr)){
                grantDate = LocalDate.parse(grantDateStr);
            }
            String equityCode = (String) payloadMap.get("equityCode");
            String transSerno = (String) payloadMap.get("transSerno");

            SmbEquityStatusQueryDTO smbEquityStatusQueryDTO = new SmbEquityStatusQueryDTO();
            smbEquityStatusQueryDTO.setDepositSerno(depositSerno);
            smbEquityStatusQueryDTO.setGrantDate(grantDate);
            smbEquityStatusQueryDTO.setEquityCode(equityCode);
            smbEquityStatusQueryDTO.setTransSerno(transSerno);

            programmaticValidator.validate(smbEquityStatusQueryDTO);
            result = portalSmbOrderService.queryStatus(smbEquityStatusQueryDTO);
        } catch (Exception e) {
            log.error("receivePoyanEquityStatusQuery error", e);
            return SmbReponseBuilders.fail(this.poyanSnbSdk, params.get("channelSerialNo"), e).build();
        }

        // 创建响应payload
        return SmbReponseBuilders.ok(this.poyanSnbSdk, params.get("channelSerialNo"))
                .add("equityStatus", result.getConvertedStatus())
                .add("equityCode", result.getSmbOrder().getSecret())
                .build();
    }



    /**
     * 响应构建器
     */
    public static class SmbReponseBuilders {

        public static SmbResponseBuilder ok(SnbSdk snbSdk, String channelSerialNo) {
            return new SmbResponseBuilder(snbSdk, channelSerialNo)
                    .add("rtnCode", "0000")
                    .add("rtnDesc", "成功");
        }

        public static SmbResponseBuilder fail(SnbSdk snbSdk, String channelSerialNo, Exception e) {
            return new SmbResponseBuilder(snbSdk, channelSerialNo)
                    .add("rtnCode", "9999")
                    .add("rtnDesc", e.getMessage());
        }


        public static class SmbResponseBuilder {

            private SnbSdk snbSdk;
            private String serialNo;
            private String channelSerialNo;
            private final Map<String, Object> respMap;

            private SmbResponseBuilder(SnbSdk snbSdk, String channelSerialNo) {
                this.snbSdk = snbSdk;
                this.channelSerialNo = channelSerialNo;
                this.serialNo = IdUtil.getSnowflakeNextIdStr();
                this.respMap = new HashMap<>();
            }

            public SmbResponseBuilder add(String key, Object value) {
                respMap.put(key, value);
                return this;
            }

            public Map build() {
                try {
                    log.info("build resp payloadMap: {}", respMap);
                    return this.snbSdk.getResponseContent(serialNo, channelSerialNo, respMap);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }

    }


}
