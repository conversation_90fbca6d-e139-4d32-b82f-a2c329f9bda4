package com.chili.vas.smb.portal.schedule.smb;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.common.oss.OSSManager;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.ext.smb.manager.SmbFileManager;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 * 文件上传
 * @createDate 2025/5/26 17:57
 */
@Slf4j
@Component
public class SmbOrderResultFileUploadJob extends SmbJob {


    @Autowired
    SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    SmbProperties smbProperties;

    @Autowired
    SmbFileManager smbFileManager;

    @Autowired
    private OSSManager ossManager;

    public SmbOrderResultFileUploadJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }

    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {
        String batchNo = smbApplyFileBatch.getBatchNo();
        Map map = null;
        try {
            map = smbFileManager.uploadFile(smbApplyFileBatch);
        }catch (Exception e){
            throw new SmbJobExecuteException("uploadFile error", e);
        }

        Map payload = (Map) map.get("payload");
        String resultFilePath = (String) payload.get(smbApplyFileBatch.getResultFileName());
        smbApplyFileBatchService.updateResultFilePathByBatchNo(batchNo,resultFilePath);

        //  oss文件上传
        File tempFile = new File(getLocalFilePath(smbApplyFileBatch.getResultFileName()));
        String ossPath = ossManager.upload(tempFile,"store");
        smbApplyFileBatchService.updateResultFileOssPathByBatchNo(batchNo,ossPath);
    }


    @Override
    public String getJobCode() {
        return "smb_order_result_file_upload";
    }

    protected String getLocalFilePath(String fileName) {
        if (StrUtil.endWith(smbProperties.getTempPath(), "/")) {
            return smbProperties.getTempPath() + fileName;
        }
        return smbProperties.getTempPath() + "/" + fileName;
    }

}
