package com.chili.vas.smb.portal.converter;

import com.chili.vas.smb.biz.dto.SmbEquityGrantDTO;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.chili.vas.smb.biz.entity.SmbOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/5/28 15:38
 */
@Mapper(componentModel = "spring")
public interface SmbOrderConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "remark", ignore = true)
    List<SmbOrder> toSmbOrder(List<SmbApplyFileBatchDetail> smbApplyFileBatchDetails);

    @Mapping(source = "equityCode", target = "secret")
    @Mapping(source = "equityAmt", target = "equityAmount")
    @Mapping(source = "partnerAmt", target = "partnerAmount")
    @Mapping(source = "customAmt", target = "customerAmount")
    @Mapping(source = "collectAcctNo", target = "accountNo")
    @Mapping(source = "collectAcctName", target = "accountName")
    @Mapping(source = "collectBankName", target = "bankAccountName")
    @Mapping(source = "collectBankCode", target = "bankAccountNum")
    SmbOrder dtoToSmbOrder(SmbEquityGrantDTO smbEquityGrantDTO);

}
