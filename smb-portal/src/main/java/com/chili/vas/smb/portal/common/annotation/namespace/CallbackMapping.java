package com.chili.vas.smb.portal.common.annotation.namespace;

import com.chili.vas.smb.portal.common.annotation.constant.NamespaceConstant;
import com.wftk.namespace.spring.boot.autoconfigure.annotation.NamespaceRequestMapping;
import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@NamespaceRequestMapping
@RestController
public @interface CallbackMapping {

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String namespace() default NamespaceConstant.CALLBACK;

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] value() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] path() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    RequestMethod[] method() default {};

}
