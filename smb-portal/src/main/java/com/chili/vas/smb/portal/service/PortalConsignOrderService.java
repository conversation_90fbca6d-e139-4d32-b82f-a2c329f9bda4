package com.chili.vas.smb.portal.service;

import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentCallbackInput;

/**
 * <AUTHOR>
 * @createDate 2025/6/5 11:18
 */
public interface PortalConsignOrderService {

    ConsignOrder doConsignDraw(String consignOrderNo);

    void sendQunqiuHttpConsign(ConsignOrder consignOrder);

    void receiveQunqiuCallBack(PaymentCallbackInput paymentCallbackInput);

    void handleQunqiuSchedule(ConsignOrder consignOrder);

    /**
     * 判断寄售订单是否支持手动重试处理（仅限失败状态）
     * @param consignOrder
     * @return
     */
    boolean manual(ConsignOrder consignOrder);

}
