package com.chili.vas.smb.portal.schedule.smb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.biz.service.SmbOrderService;
import com.chili.vas.smb.portal.common.enums.SmbOrderValidationStatusEnum;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 批次详情数据验证
 */
@Slf4j
@Component
public class SmbOrderValidationJob extends SmbJob {

    @Autowired
    SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    SmbApplyFileBatchDetailService smbApplyFileBatchDetailService;

    @Autowired
    SmbOrderService smbOrderService;

    public SmbOrderValidationJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }

    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {
        String batchNo = smbApplyFileBatch.getBatchNo();

        // 校验数量
        Integer total = smbApplyFileBatchDetailService.totalByBatchNo(batchNo, null);
        if (total == 0) {
            throw new SmbJobExecuteException("订单数量为空");
        }

        // 文件头数量比对
        if(!total.equals(smbApplyFileBatch.getTotalCount())){
            throw new SmbJobExecuteException("订单数量不一致");
        }

        int offset = 0;
        int batchSize = 500;

        //重复性校验
        while (offset < total) {
            List<SmbApplyFileBatchDetail> smbApplyFileBatchDetails = smbApplyFileBatchDetailService
                    .pageByBatchNo(batchNo, offset, batchSize);
            offset += batchSize;

            List<SmbApplyFileBatchDetail> errorList = new ArrayList<>();

            for (SmbApplyFileBatchDetail detail : smbApplyFileBatchDetails) {
                String code = "9999";
                Set<String> errorDesc = new HashSet<>();
                // 批量更新错误描述
                if (StrUtil.isBlank(detail.getDepositSerno())) {
                    errorDesc.add("存入编号不能为空");
                }

                if (detail.getEquityAmount() == null) {
                    errorDesc.add("权益金额不能为空");
                }

                if (detail.getCustomerAmount() == null) {
                    errorDesc.add("客户收益不能为空");
                }

                if (detail.getPartnerAmount() == null) {
                    errorDesc.add("合作方报酬不能为空");
                }

                if (detail.getGrantDate() == null) {
                    errorDesc.add("发放日期不能为空");
                }

                if (StrUtil.isBlank(detail.getAccountNo())) {
                    errorDesc.add("收款账户不能为空");
                }

                if (StrUtil.isBlank(detail.getAccountName())) {
                    errorDesc.add("收款账户名称不能为空");
                }

                if (StrUtil.isBlank(detail.getBankAccountNum())) {
                    errorDesc.add("收款方账户开户行号不能为空");
                }

                if (StrUtil.isBlank(detail.getBankAccountName())) {
                    errorDesc.add("收款方账户开户行名不能为空");
                }

                // 权益金额校验 权益金额 = 客户收益 + 合作方报酬
                if(detail.getCustomerAmount() + detail.getPartnerAmount() != detail.getEquityAmount()){
                    errorDesc.add("权益金额、客户收益、合作方报酬计算异常");
                }

                if(detail.getEquityAmount() <= 0){
                    errorDesc.add("权益金额不能为0以及不能为负数");
                }

                if(detail.getPartnerAmount() < 0){
                    errorDesc.add("合作方报酬不能为负数");
                }

                if(detail.getCustomerAmount() < 0){
                    errorDesc.add("客户收益不能为负数");
                }

                if(!errorDesc.isEmpty()){
                    detail.setCode(code);
                    detail.setFailDesc(String.join("、",errorDesc));
                    errorList.add(detail);
                }
            }

            if(!errorList.isEmpty()){
                //List<String> tempDepositSernos = errorList.stream().map(SmbApplyFileBatchDetail::getDepositSerno).toList();
                smbApplyFileBatchDetailService.updateCodeAndFailDescByBatchNoAndDepositSerno(errorList,batchNo);
            }

        }

        //TODO CODE_REVIEW_20250613_1 通过SQL实现批次内重复数据校验
        smbApplyFileBatchDetailService.updateCodeMessageByBatchNo(batchNo,SmbOrderValidationStatusEnum.PENDING.getCode(), SmbOrderValidationStatusEnum.FAILED.getCode(),"重复订单");

        // 校验本文件内是否有重复订单
        List<String> depositSernos = smbApplyFileBatchDetailService.queryRepetitionDepositSerno(batchNo);
        if(CollectionUtil.isNotEmpty(depositSernos)){
            smbApplyFileBatchDetailService.updateFailDescByBatchNoAndDepositSerno("9999", "文件内存在重复订单", batchNo, depositSernos);
        }

        //将待校验的数据修改为校验成功
        smbApplyFileBatchDetailService.updateCodeByBatchNo(batchNo,SmbOrderValidationStatusEnum.PENDING.getCode(), SmbOrderValidationStatusEnum.SUCCESS.getCode());

    }

    @Override
    public String getJobCode() {
        return "smb_order_validation";
    }

}
