package com.chili.vas.smb.portal.schedule.smb;

import cn.hutool.core.util.StrUtil;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatch;
import com.chili.vas.smb.biz.entity.SmbApplyFileBatchDetail;
import com.chili.vas.smb.biz.ext.smb.manager.properties.SmbProperties;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchDetailService;
import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
import com.chili.vas.smb.portal.common.enums.SmbOrderValidationStatusEnum;
import com.chili.vas.smb.biz.common.oss.OSSManager;
import com.chili.vas.smb.biz.common.exception.SmbJobExecuteException;
import com.chili.vas.smb.portal.schedule.smb.file.SmbApplyFileBatchDetailTxtFileReader;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 批次明细写入
 * @createDate 2025/5/27 10:07
 */
@Slf4j
@Component
public class SmbOrderBatchDetailWriteJob extends SmbJob {

    @Autowired
    SmbProperties smbProperties;

    @Autowired
    SmbApplyFileBatchService smbApplyFileBatchService;

    @Autowired
    SmbApplyFileBatchDetailService smbApplyFileBatchDetailService;

    @Autowired
    private OSSManager ossManager;


    @Autowired
    private IdGenerator idGenerator;

    public SmbOrderBatchDetailWriteJob(SmbApplyFileBatchService smbApplyFileBatchService) {
        super(smbApplyFileBatchService);
    }

    @Override
    protected void doExecute(SmbApplyFileBatch smbApplyFileBatch) {
        String batchNo = smbApplyFileBatch.getBatchNo();
        String localOrderFileName = getLocalFilePath(smbApplyFileBatch.getFileName());
        // oss 下载文件到本地
        downFileFromOssToLocal(smbApplyFileBatch.getFileOssPath(),localOrderFileName);

        // 删除该批次数据
        smbApplyFileBatchDetailService.deleteByBatchNo(batchNo);

        // 读取数据以及处理数据
        SmbApplyFileBatchDetailTxtFileReader txtFileReader = new SmbApplyFileBatchDetailTxtFileReader();
        txtFileReader.doReadFile(localOrderFileName, orderCountStr ->{
            try {
                int orderCount = Integer.parseInt(orderCountStr);
                smbApplyFileBatchService.updateTotalCountByBatchNo(batchNo,orderCount);
            }catch (Exception e){
                throw new SmbJobExecuteException("文件头订单数量格式错误", e);
            }
        }, list -> {
            if(!list.isEmpty()){
                LocalDateTime now = LocalDateTime.now();
                for (SmbApplyFileBatchDetail smbApplyFileBatchDetail :list) {
                    smbApplyFileBatchDetail.setBatchNo(batchNo);
                    smbApplyFileBatchDetail.setId(idGenerator.nextId());
                    smbApplyFileBatchDetail.setDeleted(0);
                    smbApplyFileBatchDetail.setCreateTime(now);
                    smbApplyFileBatchDetail.setUpdateTime(now);
                    //默认不成功，待后续校验时再更新
                    smbApplyFileBatchDetail.setCode(SmbOrderValidationStatusEnum.PENDING.getCode());
                }
                smbApplyFileBatchDetailService.insertBatch(list);
            }
        });

        // oss 上传
/*        File tempFile = new File(getLocalFilePath(smbApplyFileBatch.getFileName()));
        String ossPath = ossManager.upload(tempFile,"store");
        smbApplyFileBatchService.updateFileOssPathByBatchNo(batchNo,ossPath);*/
    }

    /**
     * 从OSS下载文件到本地
     * @param fileOssPath
     * @param fileName
     * @return
     */
    private void downFileFromOssToLocal(String fileOssPath,String fileName) {
        ossManager.download(fileOssPath,fileName);
     }

    protected String getLocalFilePath(String fileName) {
        if (StrUtil.endWith(smbProperties.getTempPath(), "/")) {
            return smbProperties.getTempPath() + fileName;
        }
        return smbProperties.getTempPath() + "/" + fileName;
    }


    @Override
    public String getJobCode() {
        return "smb_order_batch_detail_write";
    }

}
