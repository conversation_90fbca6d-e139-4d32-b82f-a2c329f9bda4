package com.chili.vas.smb.portal.common.enums;

public enum SmbOrderValidationStatusEnum {

    PENDING("9000", "待校验"),
    SUCCESS("0000", "校验成功"),
    FAILED("9999", "校验失败");

    private final String code;
    private final String desc;

    SmbOrderValidationStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
