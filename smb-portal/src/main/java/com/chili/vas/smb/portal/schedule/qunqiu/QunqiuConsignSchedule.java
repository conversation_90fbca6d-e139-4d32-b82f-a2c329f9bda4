package com.chili.vas.smb.portal.schedule.qunqiu;

import com.chili.vas.smb.biz.common.lock.LockManager;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.service.ConsignOrderService;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/6/11 10:16
 */
@Slf4j
@Component
public class QunqiuConsignSchedule {


    @Resource
    LockManager lockManager;

    @Resource
    PortalConsignOrderService portalConsignOrderService;

    @Resource
    ConsignOrderService consignOrderService;


    @Scheduled(cron = "0 */10 * * * ?")
    public void orderQueryTenMinutes() {

        DLock lock = lockManager.getScheduleQueryQunqiuOrderLock();

        try {
            if (lock.tryLock()) {
                List<ConsignOrder> consignOrders = consignOrderService.selectWaitHalfHourList();
                if (consignOrders.isEmpty()) {
                    return;
                }
                for (ConsignOrder consignOrder : consignOrders) {
                    try {
                        portalConsignOrderService.handleQunqiuSchedule(consignOrder);
                    } catch (Exception e) {
                        log.error("执行群秋支付查询任务异常。", e);
                    }
                }
            } else {
                log.warn(" jishou order result schedule lock get fail, please ignore...");
            }
        } finally {
            lock.unLock();
        }

    }

}
