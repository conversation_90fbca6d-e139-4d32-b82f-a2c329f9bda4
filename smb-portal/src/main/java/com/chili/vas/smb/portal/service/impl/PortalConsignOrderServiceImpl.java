package com.chili.vas.smb.portal.service.impl;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.PostConstruct;

import com.chili.vas.smb.biz.common.enums.ConsignOrderStatusEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderOperateSceneEnum;
import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
import com.chili.vas.smb.biz.entity.ConsignOrder;
import com.chili.vas.smb.biz.entity.SmbOrder;
import com.chili.vas.smb.biz.entity.SmbOrderOperateRecord;
import com.chili.vas.smb.biz.ext.qunqiu.client.QunqiuClient;
import com.chili.vas.smb.biz.ext.qunqiu.constant.QunqiuConstant;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentCallbackInput;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentInput;
import com.chili.vas.smb.biz.ext.qunqiu.input.PaymentQueryInput;
import com.chili.vas.smb.biz.ext.qunqiu.output.PaymentQueryOutput;
import com.chili.vas.smb.biz.service.*;
import com.chili.vas.smb.biz.util.BankBranchRecognizer;
import com.chili.vas.smb.biz.util.Region;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import com.chili.vas.smb.portal.service.PortalSmbBusinessService;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2025/6/5 11:18
 */
@Slf4j
@Service
public class PortalConsignOrderServiceImpl implements PortalConsignOrderService {

    @Autowired
    SmbOrderService smbOrderService;

    @Autowired
    ConsignOrderService consignOrderService;

    @Autowired
    SmbOrderOperateRecordService smbOrderOperateRecordService;

    @Autowired
    ConsignOrderChannelFailConfigService consignOrderChannelFailConfigService;

    @Autowired
    PortalSmbBusinessService smbBusinessService;

    @Autowired
    ProviceCityCodeInfoService proviceCityCodeInfoService;

    @Autowired
    BankCityCodeInfoService bankCityCodeInfoService;

    @Autowired
    QunqiuClient qunqiuClient;


    private BankBranchRecognizer bankBranchRecognizer;

    @PostConstruct
    public void init() {
        bankBranchRecognizer = new BankBranchRecognizer(proviceCityCodeInfoService);
    }

    @Override
    public ConsignOrder doConsignDraw(String consignOrderNo) {
        ConsignOrder consignOrder = consignOrderService.getByOrderNo(consignOrderNo);
        if (consignOrder == null) {
            throw new BusinessException("当前寄售订单不存在");
        }

        if (!Objects.equals(consignOrder.getStatus(), ConsignOrderStatusEnum.INIT.getValue())) {
            throw new BusinessException("寄售订单状态异常");
        }

        // 修改为寄售中
        Integer updated = consignOrderService.updateStatus(consignOrderNo, ConsignOrderStatusEnum.INIT.getValue(), ConsignOrderStatusEnum.PROCESSING.getValue());
        if (updated == 0) {
            throw new BusinessException("寄售订单状态修改失败");
        }

        ConsignOrder updateConsignOrder = new ConsignOrder();
        updateConsignOrder.setId(consignOrder.getId());
        // 优先使用精准查询城市编码
        String cityCode = bankCityCodeInfoService.findCityCodeByBraBankName(consignOrder.getBankAccountName());
        if(StrUtil.isBlank(cityCode)){
            Region region = bankBranchRecognizer.recognizeCity(consignOrder.getBankAccountName());
            if (region == null) {
                // 寄售失败
                log.error("city code discern fail. consignOrder: {}", consignOrder);
                // 需要后置校验，并进行人工处理
                return null;
            } else {
                consignOrder.setCityCode(region.getCityCode());
            }
        }else {
            consignOrder.setCityCode(cityCode);
        }
//        去除省级获取匹配
//        Region region = bankBranchRecognizer.recognizeCity(consignOrder.getBankAccountName());
//        if (region == null) {
//            region = bankBranchRecognizer.recognizeProvince(consignOrder.getBankAccountName());
//            if (region == null) {
//                // 寄售失败
//                log.error("city code discern fail. consignOrder: {}", consignOrder);
//                // 需要后置校验，并进行人工处理
//                return null;
//            } else {
//                consignOrder.setCityCode(region.getProvinceCode());
//            }
//        } else {
//            consignOrder.setCityCode(region.getCityCode());
//        }

        updateConsignOrder.setCityCode(consignOrder.getCityCode());
        consignOrderService.updateById(updateConsignOrder);

        return consignOrder;

    }

    @Override
    public void sendQunqiuHttpConsign(ConsignOrder consignOrder) {
        // 发起寄售
        PaymentInput paymentInput = new PaymentInput();
        // 转换单位
        String amount = new BigDecimal(consignOrder.getAmount()).divide(new BigDecimal("100")).stripTrailingZeros().toPlainString();
        paymentInput.setAmount(amount);
        paymentInput.setSerialNo(consignOrder.getOrderNo());
        paymentInput.setPayeeName(consignOrder.getName());
        paymentInput.setPayeeAccount(consignOrder.getBankAccountNo());
        paymentInput.setCityCode(consignOrder.getCityCode());
        paymentInput.setFlagCard(consignOrder.getFlagCard());
        paymentInput.setBrabankName(consignOrder.getBankAccountName());

        qunqiuClient.createOrder(paymentInput);
    }

    @Override
    public void receiveQunqiuCallBack(PaymentCallbackInput paymentCallbackInput) {
        if (paymentCallbackInput == null) {
            return;
        }
        Integer qunqiuStatus = paymentCallbackInput.getStatus();

        String serialNo = paymentCallbackInput.getSerialNo();
        if (StrUtil.isBlank(serialNo)) {
            log.info("serialNo is blank");
            return;
        }

        ConsignOrder consignOrder = consignOrderService.getByOrderNo(serialNo);
        if (consignOrder == null) {
            log.warn("consign order is not exist order no is {}", serialNo);
            return;
        }


        if (QunqiuConstant.Status.SUCCESS.equals(qunqiuStatus)) {
            // 修改状态
            Integer updated = consignOrderService.updateStatusAndConsignInfo(serialNo, ConsignOrderStatusEnum.PROCESSING.getValue(), ConsignOrderStatusEnum.SUCCESS.getValue(),
                    paymentCallbackInput.getOrderNo(), paymentCallbackInput.getPayChannel(), paymentCallbackInput.getRetCode(), paymentCallbackInput.getTradeDate(),
                    paymentCallbackInput.getTradeFinishDate(), paymentCallbackInput.getRemark());

            if (updated > 0) {
                // 修改苏商订单状态
                SmbOrder smbOrder = smbOrderService.getByOrderNo(consignOrder.getSmbOrderNo());
                if (smbOrder == null) {
                    log.warn("smb order is not exist order no is {}", consignOrder.getSmbOrderNo());
                    return;
                }
                smbOrderService.updateConsignInfoByOrderNo(smbOrder.getOrderNo(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue(),
                        paymentCallbackInput.getPayChannel(), paymentCallbackInput.getTradeFinishDate());

                // 新增操作记录
                SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
                smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
                smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue());
                smbOrderOperateRecord.setRequestSerial(null);
                smbOrderOperateRecord.setParams(null);
                smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.QUNQIU_CALLBACK.getValue());
                smbOrderOperateRecordService.save(smbOrderOperateRecord);

                // 通知苏商
                try {
                    smbBusinessService.notifyToSmb(smbOrder.getOrderNo());
                } catch (Exception e) {
                    log.error("notify to smb fail.", e);
                }

            } else {
                log.error("consign order status changed fail. order no is {}", serialNo);
            }

        } else if (QunqiuConstant.Status.FAIL.equals(qunqiuStatus)) {
            // 修改状态  失败不通知苏商
            Integer updated = consignOrderService.updateStatusAndConsignInfo(serialNo, ConsignOrderStatusEnum.PROCESSING.getValue(), ConsignOrderStatusEnum.FAIL.getValue(),
                    paymentCallbackInput.getOrderNo(), paymentCallbackInput.getPayChannel(), paymentCallbackInput.getRetCode(), paymentCallbackInput.getTradeDate(),
                    paymentCallbackInput.getTradeFinishDate(), paymentCallbackInput.getRemark());
            if (updated > 0) {
                // 修改苏商订单状态
                SmbOrder smbOrder = smbOrderService.getByOrderNo(consignOrder.getSmbOrderNo());
                if (smbOrder == null) {
                    log.warn("smb order is not exist order no is {}", consignOrder.getSmbOrderNo());
                    return;
                }
                smbOrderService.updateConsignInfoByOrderNo(smbOrder.getOrderNo(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue(),
                        null, null);

                // 新增操作记录
                SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
                smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
                smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_FAIL.getValue());
                smbOrderOperateRecord.setRequestSerial(null);
                smbOrderOperateRecord.setParams(null);
                smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.QUNQIU_CALLBACK.getValue());
                smbOrderOperateRecordService.save(smbOrderOperateRecord);

                // 判断是否应该手动重试
                boolean manual = manual(consignOrder);
                consignOrderService.updateManualRetryByOrderNo(consignOrder.getOrderNo(), manual);

            } else {
                log.error("consign order status changed fail. order no is {}", serialNo);
            }

        }

    }

    @Override
    public void handleQunqiuSchedule(ConsignOrder consignOrder) {
        PaymentQueryInput paymentQueryInput = new PaymentQueryInput();
        paymentQueryInput.setSerialNo(consignOrder.getOrderNo());
        PaymentQueryOutput paymentQueryOutput = qunqiuClient.queryOrder(paymentQueryInput);
        if (paymentQueryOutput.isSuccess()) {

            PaymentQueryOutput.PaymentQueryItem paymentQueryItem = paymentQueryOutput.getData();
            if (paymentQueryItem == null) {
                log.warn("consignOrder qunqiu query data is null, serialNo is {}", consignOrder.getOrderNo());
                return;
            }
            Integer qunqiuStatus = paymentQueryItem.getStatus();

            if (QunqiuConstant.Status.SUCCESS.equals(qunqiuStatus)) {
                // 修改状态
                Integer updated = consignOrderService.updateStatusAndConsignInfo(consignOrder.getOrderNo(), ConsignOrderStatusEnum.PROCESSING.getValue(), ConsignOrderStatusEnum.SUCCESS.getValue(),
                        paymentQueryItem.getOrderNo(), paymentQueryItem.getPayChannel(), paymentQueryItem.getRetCode(), paymentQueryItem.getTradeDate(),
                        paymentQueryItem.getTradeFinishDate(), paymentQueryItem.getRemark());

                if (updated > 0) {
                    // 修改苏商订单状态
                    SmbOrder smbOrder = smbOrderService.getByOrderNo(consignOrder.getSmbOrderNo());
                    if (smbOrder == null) {
                        log.warn("smb order is not exist order no is {}", consignOrder.getSmbOrderNo());
                        return;
                    }
                    smbOrderService.updateConsignInfoByOrderNo(smbOrder.getOrderNo(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue(),
                            paymentQueryItem.getPayChannel(), paymentQueryItem.getTradeFinishDate());

                    // 新增操作记录
                    SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
                    smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
                    smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue());
                    smbOrderOperateRecord.setRequestSerial(null);
                    smbOrderOperateRecord.setParams(null);
                    smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.QUNQIU_SCHEDULE_QUERY.getValue());
                    smbOrderOperateRecordService.save(smbOrderOperateRecord);
                    // 通知苏商
                    try {
                        smbBusinessService.notifyToSmb(smbOrder.getOrderNo());
                    } catch (Exception e) {
                        log.error("notify to smb fail.", e);
                    }

                } else {
                    log.error("consign order status changed fail. order no is {}", consignOrder.getOrderNo());
                }

            } else if (QunqiuConstant.Status.FAIL.equals(qunqiuStatus)) {
                // 修改状态 失败不通知苏商
                Integer updated = consignOrderService.updateStatusAndConsignInfo(consignOrder.getOrderNo(), ConsignOrderStatusEnum.PROCESSING.getValue(), ConsignOrderStatusEnum.FAIL.getValue(),
                        paymentQueryItem.getOrderNo(), paymentQueryItem.getPayChannel(), paymentQueryItem.getRetCode(), paymentQueryItem.getTradeDate(),
                        paymentQueryItem.getTradeFinishDate(), paymentQueryItem.getRemark());
                if (updated > 0) {
                    // 修改苏商订单状态
                    SmbOrder smbOrder = smbOrderService.getByOrderNo(consignOrder.getSmbOrderNo());
                    if (smbOrder == null) {
                        log.warn("smb order is not exist order no is {}", consignOrder.getSmbOrderNo());
                        return;
                    }
                    smbOrderService.updateConsignInfoByOrderNo(smbOrder.getOrderNo(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue(),
                            null, null);

                    // 新增操作记录
                    SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
                    smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
                    smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_FAIL.getValue());
                    smbOrderOperateRecord.setRequestSerial(null);
                    smbOrderOperateRecord.setParams(null);
                    smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.QUNQIU_SCHEDULE_QUERY.getValue());
                    smbOrderOperateRecordService.save(smbOrderOperateRecord);

                    // 判断是否应该手动重试
                    boolean manual = manual(consignOrder);
                    consignOrderService.updateManualRetryByOrderNo(consignOrder.getOrderNo(), manual);

                } else {
                    log.error("consign order status changed fail. order no is {}", consignOrder.getOrderNo());
                }
            }

        } else if (paymentQueryOutput.isOrderNotExsit()) {
            String remark = paymentQueryOutput.getResultMsg();
            if (StrUtil.isBlank(consignOrder.getCityCode())) {
                remark = "城市编码识别失败";
            }
            // 修改状态 失败不通知苏商
            Integer updated = consignOrderService.updateStatusAndConsignInfo(consignOrder.getOrderNo(), ConsignOrderStatusEnum.PROCESSING.getValue(), ConsignOrderStatusEnum.FAIL.getValue(),
                    null, null, null, null,
                    null, remark);
            if (updated > 0) {
                // 修改苏商订单状态
                SmbOrder smbOrder = smbOrderService.getByOrderNo(consignOrder.getSmbOrderNo());
                if (smbOrder == null) {
                    log.warn("smb order is not exist order no is {}", consignOrder.getSmbOrderNo());
                    return;
                }
                smbOrderService.updateConsignInfoByOrderNo(smbOrder.getOrderNo(), SmbOrderStatusEnum.CONSIGN_PROCESSING.getValue(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue(),
                        null, null);

                // 新增操作记录
                SmbOrderOperateRecord smbOrderOperateRecord = new SmbOrderOperateRecord();
                smbOrderOperateRecord.setSmbOrderNo(smbOrder.getOrderNo());
                smbOrderOperateRecord.setStatus(SmbOrderStatusEnum.CONSIGN_FAIL.getValue());
                smbOrderOperateRecord.setRequestSerial(null);
                smbOrderOperateRecord.setParams(null);
                smbOrderOperateRecord.setScene(SmbOrderOperateSceneEnum.QUNQIU_SCHEDULE_QUERY.getValue());
                smbOrderOperateRecordService.save(smbOrderOperateRecord);

                // 订单不存在可以手动重试
                consignOrderService.updateManualRetryByOrderNo(consignOrder.getOrderNo(), true);
            }

        }
    }

    @Override
    public boolean manual(ConsignOrder consignOrder) {
        if (consignOrder == null) {
            return false;
        }
        if (ConsignOrderStatusEnum.FAIL.getValue().equals(consignOrder.getStatus())) {
            return consignOrderChannelFailConfigService.getManual(consignOrder.getPayChannel(),
                    consignOrder.getRetCode(), consignOrder.getRemark());
        }
        return false;
    }


}
