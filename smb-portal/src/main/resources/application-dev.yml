spring:
  datasource:
    url: *******************************************************************************************************************************************
    username: dev
    password: dev
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 10000
      time-between-eviction-runs-millis: 3600000
  data:
    redis:
      host: redis.vas.chili.com
      port: 6379
      timeout: 2000
      connect-timeout: 1000
  redis:
    redisson:
      config:
        singleServerConfig:
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 20
          connectionMinimumIdleSize: 5
          connectionPoolSize: 20

config:
  smb:
#    temp-path: /opt/apps/vas_smb/portal/temp
    prop-chili-path: snbconfig-chili.properties
    prop-poyan-path: snbconfig-poyan.properties
    temp-path: D:\temp\smb

  qunqiu:
    client-id: smb_qunqiu
    domain: "http://***************:8500/openapi"
    secret: 123456
    platformCode: smb
  message: #消息提醒
    mail:
      enable: false
    feishu:
      robot:
        webhook: https://open.feishu.cn/open-apis/bot/v2/hook/3826abc9-28cd-41df-9e5b-03390c3f9275
        secret: 86XFUqdKvqsOQacqeR8nnc
        template-path: /template/feishu

  file:
    oss:
      store:
        bucket: taiyi-oss-test
        access-key: LTAI5t7FYh2chmdE2SHixHjt
        access-secret: ******************************
        directory: vas_smb
        cloud-endpoint: "https://oss-cn-shanghai.aliyuncs.com"
        readable: true
        writeable: true
        expiredInTimeSeconds: 180












