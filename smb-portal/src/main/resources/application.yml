spring:
  application:
    name: smb-portal
  profiles:
    active: dev
  main:
    allow-circular-references: true

server:
  port: 8100


mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

management:
  server:
    port: 8101
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /management
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always


springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      packages-to-scan: com.chili.vas.guoren
knife4j:
  enable: true
  setting:
    language: zh_cn


config:
  signature:
    filter:
      ignored-patterns:
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /internal/**

  ignored:
    client-id:
      url: #忽略clientId的校验路径
        - /management/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /v3/api-docs/**
        - /favicon.ico
        - /doc.html
        - /webjars/**
        - /internal/**

  token:
    ignore-patterns:
      - /management/**
      - /swagger-ui/**
      - /swagger-ui.html
      - /v3/api-docs/**
      - /favicon.ico
      - /doc.html
      - /webjars/**
      - /internal/**

  mybatis:
    tenant:
      enabled: false





