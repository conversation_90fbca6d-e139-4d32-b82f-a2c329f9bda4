#profile代表当前的环境，sit1,sit2,uat,prd  其中sit1,sit2,uat是测试环境,prd是生产环境
profile=sit1
#银行给商户分配的应用编号
app.appCode=91340202MA8PHQEL9G0001
#银行给商户分配的渠道编号
app.channelId=AHPY0001
#银行给商户分配的商户编号
app.merchantId=AHPY0001
#商户侧应用证书的路径，放在classpath下
app.keyStorePath=/opt/apps/vas_smb/portal/jks/poyan/poyan_test.jks
#app.keyStorePath=smb/poyan/poyan_test.jks
#商户侧应用证书容器密码
app.keyStorePassword=123456
#商户侧应用证书密码
app.privateKeyPassword=123456
#应用证书在证书容器中的别名,如果不配置别名,将使用容器中的第一个证书
#app.keyAlias=cnsuning.com
#api接口调用地址，默认无需配置
#app.snbFsosPath=
#文件上传地址，默认无需配置
#app.snbFtsPath=
#应用证书容器类型：jks pkcs12，建议使用jks
app.keyStoreType=jks
#是否使用代理
http.useProxy=false
#代理地址
http.proxyHost=***********
#代理端口
http.proxyPort=3128
#ssl双向认证 客户端证书的路径，放在classpath下
http.clientSslCert=/opt/apps/vas_smb/portal/jks/poyan/poyan_ssl_test.jks
#http.clientSslCert=smb/poyan/poyan_ssl_test.jks
#ssl双向认证，客户端ssl证书密码
http.clientSslKeyPasswd=123456
#最大连接数 默认500
http.maxTotalConns=500
#最大路由连接数，默认50
http.maxRouteConns=50
#连接超时时间，默认5s
http.connTimeout=5000
#socket超时时间，默认30s
http.soTimeout=30000
#ssl证书容器格式  jks，pkcs12, 建议使用jks
http.keyStoreType=jks
