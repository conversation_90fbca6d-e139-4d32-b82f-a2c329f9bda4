spring:
  datasource:
    url: ****************************************************************************************************************************************************************************
    username: smb_vas_account_001_dml
    password: 2JDag3seujV2YrPN
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 10000
      time-between-eviction-runs-millis: 3600000
  data:
    redis:
      host: r-uf6nfcv6h8qp9kw26tpd.redis.rds.aliyuncs.com
      port: 16379
      database: 6
      username: smb_vas_account
      password: 7kdM2yUQWyxd4hyg
      timeout: 2000
      connect-timeout: 1000
  redis:
    redisson:
      config:
        singleServerConfig:
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 20
          connectionMinimumIdleSize: 5
          connectionPoolSize: 20

config:
  smb:
    temp-path: /opt/apps/vas_smb/portal/temp
    prop-chili-path: /opt/apps/vas_smb/portal/config/snbconfig-chili.properties
    prop-poyan-path: /opt/apps/vas_smb/portal/config/snbconfig-poyan.properties
#    temp-path: D:\temp\smb

  qunqiu:
    client-id: smb_qunqiu
    domain: "https://paymentplat.shqunqiu.com/api/openapi"
    secret: fxK1SaB443OA
    platformCode: smb
  message: #消息提醒
    mail:
      enable: false
    feishu:
      robot:
        webhook: https://open.feishu.cn/open-apis/bot/v2/hook/62e30ad6-4934-4c50-9fd1-c63d0c468c89
        secret: K1TDM3dmYZj7xAcmlHah0e
        template-path: /template/feishu

  file:
    oss:
      store:
        bucket: taiyi-oss-vas-smb
        access-key:  LTAI5t8wFUuo6Wy6XoRqvfSC
        access-secret: ******************************
        directory: vas_smb
        endpoint: ""
        cloud-endpoint: "https://oss-cn-shanghai.aliyuncs.com"
        readable: true
        writeable: true
        expiredInTimeSeconds: 180












