 package com.chili.vas.smb.portal;

 import com.chili.vas.smb.biz.common.enums.SmbOrderStatusEnum;
 import com.chili.vas.smb.biz.entity.SmbOrder;
 import com.chili.vas.smb.biz.ext.smb.manager.SmbManager;
 import com.chili.vas.smb.biz.service.SmbApplyFileBatchService;
 import com.chili.vas.smb.biz.service.SmbOrderService;
 import com.chili.vas.smb.portal.schedule.smb.*;
 import lombok.extern.slf4j.Slf4j;
 import org.junit.Test;
 import org.junit.runner.RunWith;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.boot.test.context.SpringBootTest;
 import org.springframework.test.context.junit4.SpringRunner;

 import java.math.BigDecimal;
 import java.time.format.DateTimeFormatter;
 import java.util.HashMap;
 import java.util.Map;
 import java.util.Objects;
 import java.util.UUID;

 /**
  * <AUTHOR>
  * @create 2024/6/6 10:29
  */
 @RunWith(SpringRunner.class)
 @SpringBootTest(classes = SmbPortalApplication.class)
 @Slf4j
 public class SmbApplyBatchTest {

     @Autowired
     SmbOrderBatchDetailWriteJob smbOrderBatchDetailWriteJob;

     @Autowired
     SmbOrderValidationJob smbOrderValidationJob;

     @Autowired
     SmbOrderWriteJob smbOrderWriteJob;

     @Autowired
     SmbOrderResultFileWriteJob smbOrderResultFileWriteJob;

     @Autowired
     SmbOrderResultFileUploadJob smbOrderResultFileUploadJob;

     @Autowired
     SmbOrderResultFileNotifyJob smbOrderResultFileNotifyJob;

     @Autowired
     SmbManager smbManager;

     @Autowired
     SmbOrderService smbOrderService;

     @Autowired
     SmbApplyFileBatchService smbApplyFileBatchService;

     @Test
     public void sendNotify() throws Exception {
         notify1("PTDQ2025060404043440");

         notify1("PTDQ2025060404043468");

         notify1("PTDQ2025060404043474");


         notify1("PTDQ2025060404043477");

         notify1("PTDQ2025060404043484");

         notify1("PTDQ2025060404043492");

         notify1("PTDQ2025060404043522");

         notify1("PTDQ2025060404043529");

         notify1("PTDQ2025060404043532");

         notify1("PTDQ2025060404043537");

         notify1("PTDQ2025060404043540");

         notify1("PTDQ2025060404043547");

     }

     private void notify1(String depositSerno) throws Exception {
         String serilNo = UUID.randomUUID().toString().replace("-", "");
         SmbOrder smbOrder = smbOrderService.getOneByDepositSerno(depositSerno);
         String transStatus = "";
         if(Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_SUCCESS.getValue())){
             transStatus = "02";
         }else if(Objects.equals(smbOrder.getStatus(), SmbOrderStatusEnum.CONSIGN_FAIL.getValue())){
             transStatus = "03";
         }
         // 定义格式 (yyyyMMdd)
         DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
         Map payload = new HashMap<>();
         payload.put("merchantId",smbManager.getPoyanMerchantId());
         payload.put("depositSerno",smbOrder.getDepositSerno());
         payload.put("grantDate",smbOrder.getGrantDate().format(formatter));
         payload.put("equityCode",smbOrder.getSecret());
         payload.put("equityAmt",fenToYuanStr(smbOrder.getEquityAmount()));
         payload.put("partnerAmt",fenToYuanStr(smbOrder.getPartnerAmount()));
         payload.put("customAmt",fenToYuanStr(smbOrder.getCustomerAmount()));
         payload.put("transStatus",transStatus);
         payload.put("partnerRtnCode","0000");
         payload.put("partnerRtnDesc","成功");
         smbManager.fundReceivedCallback(serilNo,payload);
     }

     private String fenToYuanStr(Integer amt){
         if(amt == null){
             return "";
         }
         return new BigDecimal(amt).divide(new BigDecimal("100")).toPlainString();
     }


     @Test
     public void detailWrite() throws Exception {
//         SmbApplyFileBatch smbApplyFileBatch = smbApplyFileBatchService.getByBatchNo("686410590762949");
//         smbOrderBatchDetailWriteJob.doExecute(smbApplyFileBatch);
     }

     @Test
     public void valid() throws Exception {
//         SmbApplyFileBatch smbApplyFileBatch = smbApplyFileBatchService.getByBatchNo("686410590762949");
//         smbOrderValidationJob.doExecute(smbApplyFileBatch);
     }

     @Test
     public void orderWrite() throws Exception {
//         smbOrderWriteJob.doExecute("1930542197968289792");
     }

     @Test
     public void fileWrite() throws Exception {
//         smbOrderResultFileWriteJob.doExecute("1930542197968289792");
     }

     @Test
     public void fileUpload() throws Exception {
//         smbOrderResultFileUploadJob.doExecute("1930542197968289792");
     }

     @Test
     public void fileNotify() throws Exception {
//         smbOrderResultFileNotifyJob.doExecute("1930542197968289792");
     }

 }
