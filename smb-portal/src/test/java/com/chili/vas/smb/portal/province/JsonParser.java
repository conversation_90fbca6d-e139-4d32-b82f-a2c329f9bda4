package com.chili.vas.smb.portal.province;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 14:01
 */
public class JsonParser {

    // 定义与JSON结构对应的Java类
    static class Province {
        String code;
        String region;
        List<RegionEntity> regionEntitys; // 注意字段名与JSON中的键完全一致

        @Override
        public String toString() {
            return "Province{" +
                    "code='" + code + '\'' +
                    ", region='" + region + '\'' +
                    ", regionEntitys=" + regionEntitys +
                    '}';
        }
    }

    static class RegionEntity {
        String code;
        String region;

        @Override
        public String toString() {
            return "RegionEntity{" +
                    "code='" + code + '\'' +
                    ", region='" + region + '\'' +
                    '}';
        }

    }

}
