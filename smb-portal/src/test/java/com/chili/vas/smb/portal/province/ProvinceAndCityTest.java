package com.chili.vas.smb.portal.province;

import cn.hutool.core.util.IdUtil;
import com.chili.vas.smb.biz.entity.ProviceCityCodeInfo;
import com.chili.vas.smb.biz.mapper.ProviceCityCodeInfoMapper;
import com.chili.vas.smb.biz.service.ProviceCityCodeInfoService;
import com.chili.vas.smb.biz.util.BankBranchRecognizer;
import com.chili.vas.smb.biz.util.Region;
import com.chili.vas.smb.portal.SmbPortalApplication;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileReader;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 14:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SmbPortalApplication.class)
@Slf4j
public class ProvinceAndCityTest {

    @Autowired
    ProviceCityCodeInfoMapper proviceCityCodeInfoMapper;

    @Autowired
    ProviceCityCodeInfoService proviceCityCodeInfoService;

    @Test
    public void read(){

        String filePath = "D:\\temp\\城市编码.json"; // JSON文件路径
        List<ProviceCityCodeInfo> proviceCityCodeInfos = new ArrayList<>();
        try (FileReader reader = new FileReader(filePath)) {
            // 创建Gson解析器实例
            Gson gson = new Gson();

            // 创建TypeToken来指定解析为Province对象的列表
            java.lang.reflect.Type provinceListType = new TypeToken<List<JsonParser.Province>>(){}.getType();

            // 解析JSON到Java对象
            List<JsonParser.Province> provinces = gson.fromJson(reader, provinceListType);

            // 打印解析结果
            for (JsonParser.Province province : provinces) {
                ProviceCityCodeInfo proviceCityCodeInfo = new ProviceCityCodeInfo();
                proviceCityCodeInfo.setId(IdUtil.getSnowflakeNextId());
                proviceCityCodeInfo.setCode(province.code);
                proviceCityCodeInfo.setRegion(province.region);
                proviceCityCodeInfo.setParentCode("0");
                proviceCityCodeInfo.setDeleted(0);
                proviceCityCodeInfo.setCreateTime(LocalDateTime.now());
                proviceCityCodeInfo.setUpdateTime(LocalDateTime.now());
                proviceCityCodeInfo.setType(1);
                proviceCityCodeInfos.add(proviceCityCodeInfo);
                for (JsonParser.RegionEntity city : province.regionEntitys) {
                    ProviceCityCodeInfo proviceCityCodeInfo1 = new ProviceCityCodeInfo();
                    proviceCityCodeInfo1.setId(IdUtil.getSnowflakeNextId());
                    proviceCityCodeInfo1.setCode(city.code);
                    proviceCityCodeInfo1.setRegion(city.region);
                    proviceCityCodeInfo1.setParentCode(province.code);
                    proviceCityCodeInfo1.setDeleted(0);
                    proviceCityCodeInfo1.setCreateTime(LocalDateTime.now());
                    proviceCityCodeInfo1.setUpdateTime(LocalDateTime.now());
                    proviceCityCodeInfo1.setType(2);
                    proviceCityCodeInfo1.setParentRegion(province.region);
                    proviceCityCodeInfos.add(proviceCityCodeInfo1);
                }
            }
            proviceCityCodeInfoMapper.insertBatch(proviceCityCodeInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void test(){
        BankBranchRecognizer recognizer = new BankBranchRecognizer(proviceCityCodeInfoService);

        // 测试用例
        String[] testBranches = {
                "江苏苏商银行股份有限公司",
                "中国工商银行北京市朝阳支行",
                "中国农业银行河北省分行石家庄长安支行",
                "建设银行上海浦东分行",
                "招商银行深圳南山支行",
                "中国银行广州天河支行",
                "交通银行天津市分行",
                "中信银行杭州分行",
                "民生银行南京鼓楼支行",
                "兴业银行福州台江支行",
                "浦发银行北京海淀分理处",
                "光大银行重庆江北支行",
                "平安银行武汉分行营业部",
                "华夏银行苏州工业园区支行",
                "广发银行东莞分行",
                "渤海银行天津滨海新区分行",
                "浙商银行宁波分行",
                "北京银行中关村支行",
                "上海银行浦东分行",
                "江苏银行南京分行营业部",
                "成都银行绵阳分行营业部",
                "成都银行成都分行营业部"
        };

        System.out.println("支行名称识别结果：");
        System.out.println("======================================================================");
        System.out.printf("%-40s %-15s %-15s%n", "支行名称", "识别省份", "识别城市");
        System.out.println("----------------------------------------------------------------------");

        for (String branch : testBranches) {
            Region region = recognizer.recognizeCity(branch);
            if(region == null){
                region = recognizer.recognizeProvince(branch);
            }
            System.out.printf("%-40s %-15s %-15s%n", branch, region.getProvince(), region.getCity());
        }

        System.out.println("======================================================================");
    }
}
