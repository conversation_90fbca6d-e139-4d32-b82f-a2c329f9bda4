package com.chili.vas.smb.portal.qunqiu;


import com.chili.vas.smb.portal.SmbPortalApplication;
import com.chili.vas.smb.portal.service.PortalConsignOrderService;
import com.chili.vas.smb.portal.service.PortalSmbOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2024/6/6 10:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SmbPortalApplication.class)
@Slf4j
public class QunqiuTest {

    @Autowired
    PortalSmbOrderService portalSmbOrderService;

    @Autowired
    PortalConsignOrderService portalConsignOrderService;

    // @Test
    // public void consign() {
    //     String depositSerno = "ZHLS0000228200013309";
    //     String requestSerial = "123124";
    //     String paramsStr = "";
    //     String consign = portalSmbOrderService.consign(depositSerno, requestSerial, paramsStr);
    //     portalConsignOrderService.doConsignDraw(consign);
    // }


}
